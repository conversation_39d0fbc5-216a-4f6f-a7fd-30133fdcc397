<?php

namespace App\Http\Controllers\LeadSources;

use App\Http\Controllers\Controller;
use App\Services\Export\ExcelExportService;
use App\Services\Orders\OrderAPIService;
use GlobalHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use SellersHelper;

class ExcelImportsController extends Controller{
    /**
     * constructor.
     */
    protected $ExcelExportService;
    protected $currentSeller;
    protected $OrderAPIService;
    public function __construct(){
        // Initialize a new spreadsheet object.
        $this->ExcelExportService = new ExcelExportService();
        $this->OrderAPIService = new OrderAPIService();

        // Current Seller
        $this->currentSeller = SellersHelper::CurrentSeller();
    }


    /**
     * Import orders via Excel.
     */
    public function import(Request $request){

        // Validate the request
        $response = $this->validateExcelFile($request);
        if ($response['response'] === 'error') {
            return response()->json($response, 400);
        }

        try {
            // Process the Excel file and generate a unique name for the seller
            $exportData = $this->ExcelExportService->getDataFromExcel([
                'fileName' => 'file',
                'newName'  => $this->currentSeller->id . "-" . uniqid(),
            ]);

            // Formatter Data
            $orderData = $this->formatExportOrders($exportData);

            // Send Order via api
            $responseOrders = $this->OrderAPIService->sendOrders([
                'orderData' => $orderData,
                'sellerId' => $this->currentSeller->id
            ]);

            // Check if the response contains an error
            if ($responseOrders['response'] === 'error') {
                return response()->json($responseOrders, 400);
            }

            // Return Response
            return response()->json($responseOrders, 200 );

        } catch (\Exception $e) {
            // Log the error for further investigation
            Log::error('Error processing the Excel file or sending orders', ['exception' => $e]);
            return response()->json(['response' => 'error', 'message' => 'An unexpected error occurred'], 500);
        }
    }

    /**
     * formatter Export Data
     */
    private function formatExportOrders($exportData){
        $listeOrders = [];

        if (!empty($exportData)) {
            foreach ($exportData as $dataRow) {
                // Extract product details
                $productNames = isset($dataRow['J']) ? explode('/', $dataRow['J']) : [];
                $skuList = isset($dataRow['K']) ? explode('/', $dataRow['K']) : [];
                $quantityList = isset($dataRow['L']) ? explode('/', $dataRow['L']) : [];

                // Build SKU details list
                $skuDetailList = array_map(function ($sku, $index) use ($productNames, $quantityList) {
                    return [
                        "name"   => GlobalHelper::RemoveEmoji($productNames[$index] ?? ''),
                        "skuNo"  => $sku,
                        "skuQty" => $quantityList[$index] ?? 1,
                    ];
                }, $skuList, array_keys($skuList));

                // Add order data
                $listeOrders[] = [
                    "storeName"          => $dataRow['A'] ?? null,
                    "orderCode"          => $dataRow['C'] ?? null,
                    "consigneeCountry"   => $dataRow['D'] ?? null,
                    "consigneeContact"   => GlobalHelper::RemoveEmoji($dataRow['E'] ?? ''),
                    "consigneeMobile"    => GlobalHelper::RemoveEmoji($dataRow['F'] ?? ''),
                    "whatsappPhone"      => GlobalHelper::RemoveEmoji($dataRow['G'] ?? ''),
                    "consigneeArea"      => GlobalHelper::RemoveEmoji($dataRow['H'] ?? ''),
                    "consigneeCity"      => GlobalHelper::RemoveEmoji($dataRow['I'] ?? ''),
                    "goodsDescription"   => GlobalHelper::RemoveEmoji($dataRow['J'] ?? ''),
                    "productVaritante"  => GlobalHelper::RemoveEmoji($dataRow['J'] ?? ''),
                    "skuDetailList"      => $skuDetailList,
                    "goodsValue"         => $dataRow['M'] ?? null,
                    "currency"           => $dataRow['N'] ?? null,
                    "ProductLink"        => $dataRow['O'] ?? null,
                    "comment_shipping"   => GlobalHelper::RemoveEmoji($dataRow['P'] ?? ''),
                    "note"               => GlobalHelper::RemoveEmoji($dataRow['Q'] ?? ''),
                    "orderSource"        => "excel",
                ];
            }
        }

        // Return List of orders
        return $listeOrders;
    }


    /**
     * Validate the uploaded Excel file.
     */
    private function validateExcelFile(Request $request): ?array{
        // Validate the uploaded file using Laravel's Validator
        $validator = Validator::make($request->all(), [
            'file' => ['required', 'file', 'mimes:xls,xlsx,csv'],
        ]);

        // Check if validation fails
        if ($validator->fails()) {
            return [
                'response' => 'error',
                'message'  => implode(", ", $validator->messages()->all()),
            ];
        }

        // Return success response if validation passes
        return [
            'response' => 'success',
        ];
    }
}