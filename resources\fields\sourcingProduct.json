{"table": "seller_sourcing_product", "fields": {"id": {"dbName": "id", "layout": "all"}, "sourcingRequestId": {"dbName": "sourcing_request_id", "layout": "all"}, "sourcingRequestCode": {"dbName": "sourcing_request_code", "layout": "all"}, "name": {"dbName": "name", "layout": "all,general,details,variantes", "createValidation": "required|string|max:255", "updateValidation": "required|string|max:255"}, "arabicName": {"dbName": "arabic_name", "layout": "all"}, "productLink": {"dbName": "product_link", "layout": "all", "createValidation": "required|string", "updateValidation": "required|string"}, "ImageUrl": {"dbRAW": "'' AS productSellerImage", "layout": "all,general,details", "createValidation": "required|string", "updateValidation": "required|string"}, "createdBy": {"dbName": "created_by", "layout": "all", "createValidation": "required|exists:users,id", "updateValidation": "required|exists:users,id"}, "updatedBy": {"dbName": "updated_by", "layout": "all", "createValidation": "nullable|exists:users,id", "updateValidation": "nullable|exists:users,id"}, "category": {"dbName": "categorie_name", "layout": "all", "createValidation": "required|string|max:255", "updateValidation": "required|string|max:255"}, "isTest": {"dbName": "is_test", "layout": "all"}, "processMode": {"dbName": "process_mode", "layout": "all", "createValidation": "nullable|in:standard,express,custom", "updateValidation": "nullable|in:standard,express,custom"}, "originCountry": {"dbName": "origin_country", "layout": "all"}, "destinationCountry": {"dbName": "destination_country", "layout": "all"}, "quantity": {"dbName": "quantity", "layout": "all"}, "shippingMethod": {"dbName": "shipping_method", "layout": "all", "createValidation": "nullable|in:standard,express,air,sea", "updateValidation": "nullable|in:standard,express,air,sea"}, "note": {"dbName": "note", "layout": "all"}, "agreedPrice": {"dbName": "agreed_price", "layout": "all"}}}