<?php
namespace App\Services\Notifications;
use P<PERSON><PERSON>ailer\PHPMailer\PHPMailer;
use P<PERSON>Mailer\PHPMailer\Exception;

class MailService
{
    /*
        Send Email
    */
    public function SendMail($data)
    {
        // Include PHP Mailer library
        require_once(base_path('public/library/PHPMailer/src/Exception.php'));
        require_once(base_path('public/library/PHPMailer/src/PHPMailer.php'));
        require_once(base_path('public/library/PHPMailer/src/SMTP.php'));

        $mail = new PHPMailer(true);

        try {
             // SMTP configuration from .env
             $mail->isSMTP();
             $mail->Host       = env('MAIL_HOST');       // SMTP server from .env
             $mail->SMTPAuth   = true;
             $mail->Username   = env('MAIL_USERNAME');   // SMTP username from .env
             $mail->Password   = env('MAIL_PASSWORD');   // SMTP password from .env
             $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS; // Use 'tls' as per your .env setting
             $mail->Port       = env('MAIL_PORT');       // Port from .env (usually 465 or 587)

             // Sender and recipient
             $mail->setFrom(env('MAIL_FROM_ADDRESS'), env('MAIL_FROM_NAME')); // From address and name from .env
             $mail->addAddress($data['toEmail']); // Dynamic recipient email address

            // Email content
            $mail->isHTML(true);
            $mail->Subject = $data['subject']; // Dynamic subject
            $mail->Body    = $data['body']; // Dynamic body

            // Send email
            $mail->send();
        } catch (Exception $e) {
            // If email could not be sent, return error
            throw new \Exception("Message could not be sent. Mailer Error: {$mail->ErrorInfo}");
        }
    }
}