<?php
namespace App\Services\Notifications;

use Illuminate\Support\Facades\Log;
use P<PERSON>Mailer\PHPMailer\PHPMailer;
use P<PERSON><PERSON>ailer\PHPMailer\Exception;

class MailService
{
    /*
        Send Email
    */
    public function SendMail($data)
    {
        // Include PHP Mailer library
        require_once(base_path('public/library/PHPMailer/src/Exception.php'));
        require_once(base_path('public/library/PHPMailer/src/PHPMailer.php'));
        require_once(base_path('public/library/PHPMailer/src/SMTP.php'));

        $mail = new PHPMailer(true);

        try {
             // SMTP configuration from config
             $mail->isSMTP();
             $mail->Host       = config('mail.mailers.smtp.host');
             $mail->SMTPAuth   = true;
             $mail->Username   = config('mail.mailers.smtp.username');
             $mail->Password   = config('mail.mailers.smtp.password');
             $mail->SMTPSecure = PHPMailer::ENCRYPTION_STARTTLS;
             $mail->Port       = config('mail.mailers.smtp.port');

             // Sender and recipient
             $mail->setFrom(config('mail.from.address'), config('mail.from.name'));
             $mail->addAddress($data['toEmail']); // Dynamic recipient email address

            // Email content
            $mail->isHTML(true);
            $mail->Subject = $data['subject']; // Dynamic subject
            $mail->Body    = $data['body']; // Dynamic body


            // Send email
            $mail->send();

        } catch (Exception $e) {
            // If email could not be sent, return error
            throw new \Exception("Message could not be sent. Mailer Error: {$e->getMessage()}");
        }
    }
}