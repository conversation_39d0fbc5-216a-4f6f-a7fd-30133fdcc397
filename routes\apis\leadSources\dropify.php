<?php

use App\Http\Controllers\LeadSources\DropifyController;
use App\Http\Controllers\LeadSources\YoucanImportsController;
use Illuminate\Support\Facades\Route;


Route::prefix('dropify')->name('dropify.')->group(function () {

    Route::get('/auth/authorize', [DropifyController::class, 'isDropifyRequest'])->middleware('auth.token');
    Route::get('/auth/install', [DropifyController::class, 'install'])->middleware('auth.token');
    Route::post('/orders/create', [DropifyController::class, 'setOrders'])->middleware('auth.dropify');

});