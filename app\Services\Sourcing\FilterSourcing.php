<?php

namespace App\Services\Sourcing;

use App\Models\SourcingProduct;
use App\Models\SourcingRequest;
use App\Services\Fields\FieldsService;
use App\Services\Fields\FormatterSourcing;
use App\Traits\FilterQuery;
use Illuminate\Support\Facades\DB;
use MediaHelper;
use SellersHelper;

class FilterSourcing
{
    protected $data;
    protected $query;
    protected $baseTable;
    protected $FieldsService;
    protected $FormatterSourcing;
    protected $currentSeller;
    protected $columnsType;

    use FilterQuery;

    public function __construct()
    {
        $this->FieldsService = new FieldsService();
        $this->FormatterSourcing = new FormatterSourcing();
        $this->currentSeller = SellersHelper::CurrentSeller();
        $this->baseTable = 'sellers_sourcing_request';
        $this->columnsType = 'sourcingRequest';
    }



    /**
     * Get filtered sourcing requests based on input data.
     */
    public function getSourcingRequests(array $data)
    {
        // Store input data in the class property
        $this->data = $data;

        // Initialize query with default filtering conditions
        $this->initializeQuery();

        // Apply filters
        $this->applyStatusFilters();
        $this->applyDateFilters();
        $this->applyProductFilters();

        // Apply custom column selection based on input or defaults
        $this->applyCustomColumns();

        // Apply ordering based on user-specified or default parameters
        $this->applyOrdering();

        // Fetch and return the final filtered results
        return $this->fetchResults($this->FormatterSourcing);
    }

    /**
     * Initialize the base query with default conditions.
     */
    private function initializeQuery(): void
    {
        $this->query = SourcingRequest::where("{$this->baseTable}.seller_id", $this->currentSeller->id)
            ->where("{$this->baseTable}.status", '!=', 'abandoned');
    }

    /**
     * Apply status filters to the query.
     */
    private function applyStatusFilters(): void
    {
        $status = $this->data['status'] ?? null;

        if ($status) {
            $this->query->where("{$this->baseTable}.status", $status);
        }
    }

    /**
     * Apply date filters to the query.
     */
    private function applyDateFilters(): void
    {
        $startDate = $this->data['startDate'] ?? null;
        $endDate = $this->data['endDate'] ?? null;

        if ($startDate) {
            $this->query->whereDate("{$this->baseTable}.created_at", '>=', $startDate);
        }
        if ($endDate) {
            $this->query->whereDate("{$this->baseTable}.created_at", '<=', $endDate);
        }
    }

    /**
     * Apply product reference filters to the query.
     */
    private function applyProductFilters(): void
    {
        $productReference = $this->data['productReference'] ?? null;

        // Exit early if no product reference is provided
        if ($productReference) {
            // Exit if the product is not found in stock
            $rowStock = \App\Models\SellersStock::find($productReference);

            // Retrieve all variant products if the product is variable
            $variantIds = [];
            if (($rowStock->id ?? null) && $rowStock->product_type === "variable") {
                $variantIds = (new \App\Services\Warehousing\FilterProducts())->getProducts([
                    'layout' => 'all',
                    'parent' => $rowStock->id,
                ]);
            }

            // Apply filtering based on the product reference and its variants
            $this->query->whereExists(function ($query) use ($productReference, $variantIds) {
                $query->select(DB::raw(1))
                    ->from('seller_sourcing_product')
                    ->whereRaw('seller_sourcing_product.sourcing_request_id = sellers_sourcing_request.id')
                    ->where(function ($q) use ($productReference, $variantIds) {
                        $q->where('seller_sourcing_product.name', 'like', "%{$productReference}%")
                            ->orWhere('seller_sourcing_product.arabic_name', 'like', "%{$productReference}%");

                        if (!empty($variantIds)) {
                            foreach ($variantIds['items'] as $variant) {
                                $q->orWhere('seller_sourcing_product.name', 'like', "%{$variant['id']}%")
                                    ->orWhere('seller_sourcing_product.arabic_name', 'like', "%{$variant['id']}%");
                            }
                        }
                    });
            });
        }
    }

    /**
     * Apply custom column selection based on input or defaults.
     */
    private function applyCustomColumns(): void
    {
        $customColumns = $this->data['custom_columns'] ?? [];

        if (!empty($customColumns)) {
            $this->query->select($customColumns);
        } else {
            $this->data = $this->FieldsService->DefaultColumns($this->columnsType, $this->data);
            if (!empty($this->data['columns'])) {
                // Exclude seller and user-related fields
                $columns = array_filter($this->data['columns'], function($column) {
                    $excludedFields = [
                        'seller_id',
                        'seller_name',
                        'created_by',
                        'updated_by'
                    ];
                    foreach ($excludedFields as $field) {
                        if (strpos($column, $field) !== false) {
                            return false;
                        }
                    }
                    return true;
                });
                $this->query->select($columns);
            }
        }
    }

    /**
     * Fetch and return the final filtered results.
     */
    private function fetchResults($formatter)
    {
        $perPage = $this->data['perPage'] ?? env('PER_PAGE', 15);
        $noPaginate = $this->data['noPaginate'] ?? false;
        $count = $this->data['count'] ?? false;
        $first = $this->data['first'] ?? false;
        $noFormatter = $this->data['noFormatter'] ?? false;

        if ($count) {
            return $this->query->count();
        }

        if ($noPaginate) {
            $results = $this->query->get();
        } else {
            $results = $this->query->paginate($perPage);
        }

        // Transform results to include product counts, quantities, and formatted products string
        $results->getCollection()->transform(function ($request) {
            $products = \App\Models\SourcingProduct::where('sourcing_request_id', $request->id)
                ->whereNull('parent')
                ->get();

            $request->number_of_products = $products->count();
            $request->total_quantity = $products->sum('quantity');

            // Format products string
            $productsString = $products->map(function ($product) {
                return "{$product->quantity} * {$product->name}";
            })->join('<br>');

            $request->products = $productsString;

            return $request;
        });

        // Return results, optionally formatting them using FieldsHelper
        return $noFormatter ? $results : $this->FieldsService->ResponseResults([
            'initialResults' => $results,
            'results' => $formatter->formatterResult([
                "items" => $first == 1 ? [$results] : ($noPaginate ? $results : $results->items()),
                'first' => $first,
            ]),
            'first' => $first,
            'noPaginate' => $noPaginate,
            'layout' => $this->data['layout'] ?? null,
            'type' => $this->columnsType,
            'resultFormatted' => 1,
        ]);
    }

    public function getFormattedRequestWithProducts($id): ?array
    {
        // Fetch the sourcing request
        $sourcingRequest = $this->fetchSourcingRequest($id);

        if (!$sourcingRequest) {
            return null;
        }

        // Format the response
        return $this->formatSourcingRequestWithProducts($sourcingRequest);
    }

    private function fetchSourcingRequest($id)
    {
        return SourcingRequest::with(['products'])
            ->where('id', $id)
            ->first();
    }

    private function formatSourcingRequestWithProducts($sourcingRequest): array
    {
        return [
            'id' => $sourcingRequest->id,
            'requestCode' => $sourcingRequest->request_code,
            'status' => $sourcingRequest->status,
            'message' => $sourcingRequest->message,
            'createdAt' => $sourcingRequest->created_at,
            'updatedAt' => $sourcingRequest->updated_at,
            'products' => $this->formatProducts($sourcingRequest->products->where('parent', null)->values())
        ];
    }

    private function formatProducts($products): array
    {
        return $products->map(function ($product) {
            return [
                'id' => $product->id,
                'name' => $product->name,
                'arabicName' => $product->arabic_name,
                'category' => $product->categorie_name,
                'productLink' => $product->product_link,
                'isTest' => $product->is_test,
                'processMode' => $product->process_mode,
                'originCountry' => $product->origin_country,
                'destinationCountry' => (int) $product->destination_country,
                'quantity' => $product->quantity,
                'shippingMethod' => $product->shipping_method,
                'price' => $product->agreed_price,
                'note' => $product->note,
                'image' => MediaHelper::getFileUrl(["tableName" => "seller_sourcing_product","tableId" => $product->id]),
                'variants' => $product->variants->map(function ($variant) {
                    return [
                        'id' => $variant->id,
                        'name' => $variant->name,
                        'value' => $variant->value,
                    ];
                })->toArray()
            ];
        })->toArray();
    }

    /**
     * Get Sourcing Status for front and admin
     *
     * @return array
     */
    public function SourcingRequestStatus(){
        return [
            'statusForView' => [
                'placed' => 'Placed',
                'processing' => 'Processing',
                'parked' => 'Parked',
                'shipped' => 'Shipped',
                'delivered' => 'Delivered'
            ],
            'status' => [
                'placed' => 'Placed',
                'processing' => 'Processing',
                'parked' => 'Parked',
                'shipped' => 'Shipped',
                'delivered' => 'Delivered',
                'abandoned' => 'Abandoned'
            ]
        ];
    }

}