<?php
namespace App\Services\Fields;

use App\Models\SellersPages;
use App\Services\Fields\FieldsService;
use App\Services\Sellers\SellersService;
use GlobalHelper;
use MediaHelper;

class FormatterSellers {

    protected $FieldsService;
    protected $SellersService;

    public function __construct() {
        // Dependency Injection for better testability
        $this->FieldsService = new FieldsService();

        // Current Seller
        $this->SellersService = new SellersService();
    }
    
    /**
     * Format the orders based on provided options
     */
    public function formatterResult(array $options = []): array|null{
        // Extract options
        $first = $options['first'] ?? null;
        $items = $options['items'] ?? [];

        // Convert to array using GlobalHelper
        $items = GlobalHelper::ConvertToArray(['liste' => $items]);

        // Return empty array if no items are provided
        if (empty($items)) {
            return $first === 1 ? null : ['items' => []];
        }

        // Format the items
        $formattedItems = array_map(function ($row) {
            $groupedData = $this->FieldsService->GroupeFields('products', $row);
            return $this->FormatterSeller($groupedData);
        }, $items);

        // Return either the first item or all formatted items
        return $first === 1 ? ($formattedItems[0] ?? null) : ['items' => $formattedItems];
    }

    /**
     * Formatter for an individual order
     */
    private function FormatterSeller(array $row_seller): array{
        // Format Media
        $row_seller = $this->FormatterMedia($row_seller);

        // Permissions
        if (array_key_exists('permissions', $row_seller)) { $row_seller['permissions']= json_decode($row_seller['permissions'], true); }

        // return order
        return $row_seller;
    }

    /**
     * Formatter for the product details within an order
     */
    private function FormatterMedia(array $row_seller): array{
    
        // Check is logo
        if (array_key_exists('logo', $row_seller)) {
            $rowStore = $this->SellersService->getStore([
                'sellerId' => $row_seller['id'],
            ]);

            // Check Store
            if($rowStore->id ?? null){
                $row_seller['logo'] = MediaHelper::getFileUrl(["tableName" => "sellers_pages","tableId" => $rowStore->id,'imageSlug' => 'image']); 
            }
        }
    
        return $row_seller;
    }
}
