<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Sellers;
use App\Models\User;
use App\Services\Fields\FieldsService;
use App\Services\Sellers\FilterSellers;
use App\Services\Sellers\SellersService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use MediaHelper;
use SellersHelper;

class ProfileController extends Controller{
    protected FilterSellers $FilterSellers;
    protected FieldsService $FieldsService;
    protected SellersService $SellersService;

    /**
     * Construct
     */
    public function __construct(){
        $this->FilterSellers = new FilterSellers();
        $this->FieldsService = new FieldsService();
        $this->SellersService = new SellersService();
    }

    /**
     * Show Profile
     */
    public function show(Request $request){
        return response()->json([
            'response' => 'success',
            'result' => SellersHelper::renderUserInfos(),
        ], 200);
    }

    /**
     * Update Profile
     */
    public function update(Request $request){
        // Get the current seller
        $currentSeller = SellersHelper::renderUserInfos();

        // Validate request data
        $validationResult = $this->validateSellerData($request, $currentSeller->id);
        if ($validationResult['response'] === 'error') {
            return response()->json($validationResult, 400);
        }

        // Process input data
        $processedData = $this->processSellerData($request);

        // // Check email uniqueness
        // $emailCheck = $this->checkEmailUniqueness($processedData['email'], $currentSeller->id);
        // if ($emailCheck) {
        //     return response()->json([
        //         'response' => 'error',
        //         'message'  => __('auth.This email address has already been used!'),
        //     ], 400);
        // }

        // Prevent updating email
        unset($processedData['email']);
        // Update seller
        if ($this->updateSeller($currentSeller->id, $processedData)) {
            // Update the user's email after updating the seller
           // $this->updateUserEmail($currentSeller->id, $processedData['email']);

            // Save Logo
            $this->updateUserLogo($request,$currentSeller->id);

            // Success Response
            return response()->json([
                'response' => 'success',
                'result' => SellersHelper::renderUserInfos(),
            ], 200);
        }

        return response()->json([
            'response' => 'error',
            'message'  => __('global.update_failed'),
        ], 500);
    }

    /**
     * Update User Password
     */
    public function updatePassword(Request $request){
        $data = $request->all();

        // Validate the request for password fields (old and new password)
        $validateRequest = $this->validatePasswordRequest($request);
        if (($validateRequest['response'] ?? null) === 'error') {
            return response()->json($validateRequest, 400);
        }

        // Get the current seller and associated user
        $currentSeller = SellersHelper::renderUserInfos();
        $user = User::find($currentSeller->userID);

        // If the user does not exist, return an error
        if (!$user) {
            return response()->json([
                'response' => 'error',
                'message'  => __('auth.User not found'),
            ], 404);
        }

        // Check if the old password is correct
        if (!$this->checkOldPassword($data['oldPassword'], $user)) {
            return response()->json([
                'response' => 'error',
                'message'  => __('auth.Old password is incorrect'),
            ], 400);
        }

        // Update the user's password with the new one
        $this->updateUserPassword($user, $data['newPassword']);

        // Return success response
        return response()->json([
            'response' => 'success',
            'message'  => __('auth.Password updated successfully'),
        ], 200);
    }

    /**
     * update User Logo
     */
    private function updateUserLogo(Request $request, $sellerId){

        $rowStore = $this->SellersService->getStore([
            'sellerId' => $sellerId,
        ]);

        // Check Store
        if($rowStore->id ?? null){
            // Check if logo is a file in the request
            if($request->hasFile('logo')){
                // Logo is a file, save it
                MediaHelper::saveMedia([
                    'request' => $request,
                    'fileName' => "logo",
                    'imageSlug' => "image",
                    'tableName' => 'sellers_pages',
                    'tableId' => $rowStore->id,
                ]);
            } else{
                // If logo is a text URL and not empty, return without doing anything
                if (isset($request->logo) && is_string($request->logo) && !empty($request->logo)) {

                    return;
                }
                MediaHelper::deleteOldFiles([
                    'tableName' => 'sellers_pages',
                    'tableId' => $rowStore->id,
                    'imageSlug' => 'image',
                ]);
                // Logo is null, delete the existing profile logo

            }
        }
    }

    /**
     * validate Password Request
     */
    private function validatePasswordRequest(Request $request){
        // Validate that both old and new passwords meet the required conditions
        $validator = Validator::make($request->all(), [
            'oldPassword'  => 'required|string|min:6',
            'newPassword'  => 'required|string|min:6|confirmed',
        ], [
            'oldPassword.required' => 'The old password field is required.',
            'oldPassword.min' => 'The old password must be at least 6 characters.',
            'newPassword.required' => 'The new password field is required.',
            'newPassword.min' => 'The new password must be at least 6 characters.',
            'newPassword.confirmed' => 'The new password confirmation does not match.',
        ]);

        // If validation fails, return error response with validation messages
        if ($validator->fails()) {
            return [
                'response' => 'error',
                'message' => implode(", ", $validator->messages()->all()),
            ];
        }

        return ['response' => 'success'];
    }

    /**
     * check Old Password
     */
    private function checkOldPassword($oldPassword, $user){
        // Check if the provided old password matches the user's current password
        return Hash::check($oldPassword, $user->password);
    }

    /**
     * update User Password
     */
    private function updateUserPassword($user, $newPassword){
        // Update the user's password in the database
        $user->update([
            'password' => bcrypt($newPassword),
        ]);
    }


    /**
     * Check if email is already used by another user
     */
    private function checkEmailUniqueness($email, $sellerId){
        $existingUser = User::where('email', $email)->first();
        $seller = Sellers::find($sellerId);

        return $existingUser && $seller && $existingUser->id != $seller->user_id;
    }


    /**
     * Validate seller data
     */
    private function validateSellerData(Request $request, $sellerId){
        return $this->FieldsService->validateData([
            'type'          => 'sellers',
            'option'        => 'updateValidation',
            'data'          => $request->all(),
            'replaceParams' => ["{id}" => $sellerId],
        ]);
    }

    /**
     * Process seller data
     */
    private function processSellerData(Request $request){
        return $this->FieldsService->ProcessData([
            'option' => 'store',
            'type'   => 'sellers',
            'data'   => $request->all(),
        ]);
    }

    /**
     * Update seller
     */
    private function updateSeller($sellerId, $data){
        $seller = Sellers::find($sellerId);
        return $seller ? $seller->update($data) : false;
    }

    /**
     * Update the user's email
     */
    private function updateUserEmail($sellerId, $newEmail){
        $seller = Sellers::find($sellerId);
        if ($seller) {
            $user = User::find($seller->user_id);
            if ($user) {
                $user->update([
                    'name' => $newEmail,
                    'email' => $newEmail,
                ]);
            }
        }
    }
}