<?php

namespace App\Services\LeadSource;

use GlobalHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class ShopifyService
{

       /**
     * Uninstall the Shopify app from a store
     *
     * @param string $shop
     * @param string $accessToken
     * @return bool
     */
    public function uninstallApp(string $shop, string $accessToken): bool
    {
        try {
            $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Accept' => 'application/json',
                'X-Shopify-Access-Token' => $accessToken
            ])
            ->delete("https://{$shop}/admin/api_permissions/current.json");
            return $response->successful();
        } catch (\Exception $e) {
            Log::error('Shopify uninstall failed: ' . $e->getMessage());
            return false;
        }
    }


    /**
     * Validate the Shopify request.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse|bool
     */
    public function validateShopifyRequest(Request $request)
    {
        $hmac = $request->header('x-shopify-hmac-sha256');

        $data = $request->getContent();
        $calculatedHmac = base64_encode(hash_hmac('sha256', $data, env('SHOPIFY_API_SECRET'), true));


        if (!$hmac || !hash_equals($hmac, $calculatedHmac)) {
            Log::error('error');
            return response()->json([
                'response' => 'error',
                'message' => __('sourcing.request_not_authorized'),
            ], 401);
        }
        return true;
    }

    /**
     * Format a single order for export.
     *
     * @param array $dataRow
     * @return array
     */
    public function formatWebHookOrder($dataRow)
    {
        // Extract product details from line_items
        $lineItems = $dataRow['line_items'] ?? [];
        $skuDetailList = array_map(function ($item) {
            return [
            "name"   => GlobalHelper::RemoveEmoji($item['title'] ?? ''),
            "skuNo"  => $item['sku'] ?? null,
            "skuQty" => $item['quantity'] ?? 1,
            ];
        }, $lineItems);

        // Return formatted order data
        return [[
            "storeName"          => $dataRow['store_name'] ?? null,
            "orderCode"          => $dataRow['order_number'] ?? null,
            "consigneeCountry"   => $dataRow['customer']['default_address']['country'] ?? null,
            "consigneeContact"   => GlobalHelper::RemoveEmoji(($dataRow['customer']['first_name'] ?? '') . ' ' . ($dataRow['customer']['last_name'] ?? '')),
            "consigneeMobile"    => GlobalHelper::RemoveEmoji($dataRow['customer']['phone'] ?? '************'),
            "whatsappPhone"      => GlobalHelper::RemoveEmoji($dataRow['customer']['phone'] ?? '************'),
            "consigneeArea"      => GlobalHelper::RemoveEmoji($dataRow['customer']['default_address']['address1'] ?? ''),
            "consigneeCity"      => GlobalHelper::RemoveEmoji($dataRow['customer']['default_address']['city'] ?? ''),
            "goodsDescription"   => implode(' / ', array_map(function ($item) {
                return GlobalHelper::RemoveEmoji($item['title'] ?? '');
            }, $dataRow['line_items'] ?? [])),
            "productVaritante"   => implode(' / ', array_map(function ($item) {
                return GlobalHelper::RemoveEmoji($item['variant_title'] ?? '');
            }, $dataRow['line_items'] ?? [])),
            "skuDetailList"      => $skuDetailList,
            "goodsValue"         => $dataRow['current_total_price'] ?? null,
            "currency"           => $dataRow['currency'] ?? null,
            "ProductLink" => isset($dataRow['store_name'], $dataRow['line_items'][0]['product_id'])
                ? "https://{$dataRow['store_name']}/admin/products/{$dataRow['line_items'][0]['product_id']}"
                : '',
            "comment_shipping" => GlobalHelper::RemoveEmoji(
                    $dataRow['customer']['default_address']['zip']
                        ?? $dataRow['shipping_address']['zip']
                        ?? ''
                ),
            "note"               => GlobalHelper::RemoveEmoji($dataRow['note'] ?? ''),
            "orderSource"        => "shopify",
        ]];
    }


    /**
     * Validate the request parameters.
     *
     * @param Request $request
     * @param array $requiredParams
     * @return \Illuminate\Http\JsonResponse
     */
    public function validateRequestParameters(Request $request, array $requiredParams)
    {
        $validatedParams = [];
        $missingParams = [];

        foreach ($requiredParams as $param) {
            $value = $request->query($param);
            if (!$value) {
                $missingParams[] = $param;
            } else {
                $validatedParams[$param] = $value;
            }
        }

        if (!empty($missingParams)) {
            return response()->json([
                'response' => 'error',
                'message' => __('sourcing.missing_required_parameters'),
            ], 400);
        }

        return response()->json([
            'response' => 'success',
            'result' => $validatedParams
        ]);
    }


    /**
     * Check if the Shopify access token is present in the session.
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function checkShopifyToken()
    {
        $accessToken = session(env('SHOPIFY_TOKEN_SESSION_KEY'));

        if (!$accessToken) {
            return response()->json([
                'response' => 'error',
                'message' => __('sourcing.unauthorized_missing_access_token'),
            ], 401);
        }

        return response()->json([
            'response' => 'success',
            'result' => $accessToken
        ]);
    }



    /**
 * Exchange the authorization code for an access token.
 *
 * @param string $shop
 * @param string $code
 * @return \Illuminate\Http\JsonResponse
 */
    public function getAccessToken($shop, $code)
    {
        $response = Http::asForm()->post("https://{$shop}/admin/oauth/access_token", [
            'client_id' => env('SHOPIFY_API_KEY'),
            'client_secret' => env('SHOPIFY_API_SECRET'),
            'code' => $code,
        ]);

        $data = $response->json();

        if (!isset($data['access_token'])) {
            return response()->json([
                'response' => 'error',
                'message' => __('sourcing.failed_to_get_access_token')
            ], 500);
        }

        // Store the access token in session
        session([env('SHOPIFY_TOKEN_SESSION_KEY') => $data['access_token']]);

        return response()->json([
            'response' => 'success',
            'result' => $data['access_token']
        ]);
    }
}