<?php

namespace App\Http\Controllers\Invoices;

use App\Http\Controllers\Controller;
use App\Services\Invoices\FilterInvoices;
use App\Services\Invoices\InvoicesService;
use App\Services\Validator\InvoicesValidator;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use SellersHelper;

class InvoicesController extends Controller{
    protected InvoicesValidator $InvoicesValidator;
    protected FilterInvoices $FilterInvoices;
    protected InvoicesService $InvoicesService;
    protected $currentSeller;

    /**
     * Construct
     */
    public function __construct(){
        $this->InvoicesValidator = new InvoicesValidator();
        $this->FilterInvoices = new FilterInvoices();
        $this->InvoicesService = new InvoicesService();
        $this->currentSeller = SellersHelper::CurrentSeller();
    }

    /**
     * Retrieve and return a list of filtered invoices.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse{
        // Retrieve the list of invoices based on the provided filters
        $result = $this->FilterInvoices->getInvoices($request->all());

        // Prepare the base response
        $results = [
            'response' => 'success',
            'result' => $request->count ? $result : ($result['items'] ?? []),
        ];

        // Include pagination details only if count is not requested
        if (!$request->count) {
            $results['paginate'] = [
                'count' => $result['count'] ?? 0,
                'currentPage' => $result['currentPage'] ?? 1,
                'totalPages' => $result['totalPages'] ?? 1,
            ];
        }

        // Return the response as a JSON object
        return response()->json($results);
    }

    /**
     * Invoice details
     */
    public function details(Request $request,$id){
        // Validate Product
        $validateInvoice = $this->InvoicesValidator->validateInvoice(['id' => $id,'layout' => 'general,details']);
        if($validateInvoice['response'] == 'error'){ return response()->json($validateInvoice, 404); }

        // Get Invoice
        $rowInvoice = $validateInvoice["rowInvoice"] ?? [];
        $rowInvoice->recipientInfo = $this->addRecipientInfo();

        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $rowInvoice,

        ]);
    }

    /**
     * List of statuses
     */
    public function statuses(){
        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $this->InvoicesService->InvoiceStatus("list"),
        ]);
    }

    /**
     * Add invoice Recipient Info
     */
    public function addRecipientInfo(){
        $recipientInfo = (object)[
            'namePro' => $this->currentSeller->name_pro,
            'fullname' => $this->currentSeller->fullname,
            'registerNumber' => $this->currentSeller->register_number,
            'iceNumber' => $this->currentSeller->ice_number,
            'taxIdentification' => $this->currentSeller->tax_identification,
            'address' => $this->currentSeller->address,
        ];
        return $recipientInfo;
    }

}