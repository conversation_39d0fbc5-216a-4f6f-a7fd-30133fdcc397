<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Services\TwoFactorAuth\TwoFactorAuthService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use SellersHelper;

class TwoFactorAuth<PERSON>ontroller extends Controller
{
    protected $twoFactorAuthService;
    protected $currentSeller;

    public function __construct(TwoFactorAuthService $twoFactorAuthService)
    {
        $this->twoFactorAuthService = $twoFactorAuthService;
        $this->currentSeller = SellersHelper::CurrentSeller();
    }

    /**
     * Toggle 2FA
     *
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function Toggle2FA(Request $request){

        $validateToggle2FA = $this->validateToggle2FA($request);
        if ($validateToggle2FA['response'] === 'error') {
            return response()->json($validateToggle2FA, 422);
        }

        if (!Hash::check($request->password, $this->currentSeller->user->password)) {
            return response()->json([
                'response' => 'error',
                'message' => 'Invalid password',
            ], 422);
        }

        $response = $this->handleTwoFactoreService($request->enable , $this->currentSeller);

        return response()->json($response, 200);
    }



    /**
     * Verify and enable 2FA
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verify(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string|size:6',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
                'status' => 422
            ], 422);
        }

        // Verify the code
        if (!$this->twoFactorAuthService->verifyCode($request->code, $this->currentSeller)) {
            return response()->json([
                'response' => 'error',
                'message' => 'Invalid verification code',
                'status' => 422
            ], 422);
        }

        // Enable 2FA for the user
        $this->twoFactorAuthService->enable2FA($this->currentSeller);

        return response()->json([
            'response' => 'success',
            'message' => 'Two-factor authentication has been enabled',
        ]);
    }

    /**
     * Disable 2FA for the authenticated user
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function disable(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string|size:6',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
                'status' => 422
            ], 422);
        }

        // Verify the code before disabling 2FA
        if (!$this->twoFactorAuthService->verifyCode($request->code, $this->currentSeller)) {
            return response()->json([
                'response' => 'error',
                'message' => 'Invalid verification code',
                'status' => 422
            ], 422);
        }

        // Disable 2FA
        $this->twoFactorAuthService->disable2FA($this->currentSeller);

        return response()->json([
            'response' => 'success',
            'message' => 'Two-factor authentication has been disabled',
        ]);
    }

    /**
     * Get the current 2FA status
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function status()
    {
        return response()->json([
            'response' => 'success',
            'result' => [
                'enabled' => $this->twoFactorAuthService->is2FAEnabled($this->currentSeller)
            ]
        ]);
    }

    /**
     * Handle 2FA service
     */
    private function handleTwoFactoreService($enable, $seller){
        if ($enable) {
            // Generate 2FA secret and QR code
            $secretKey = $this->twoFactorAuthService->generateSecretKey($seller);

            $qrCodeUrl = $this->twoFactorAuthService->getQRCodeUrl($seller, $secretKey);

            return [
                'response' => 'success',
                'message' => '2FA setup initiated',
                'result' => [
                    'secret' => $secretKey,
                    'qr_code' => $qrCodeUrl
                ]
            ];
        } else {
            // Disable 2FA
            $this->twoFactorAuthService->disable2FA($seller);

            return [
                'response' => 'success',
                'message' => '2FA has been disabled',
            ];
        }
    }

    /**
     * Validate Toggle 2FA
     */
    private function validateToggle2FA(Request $request){
        $validator = Validator::make($request->all(), [
            'enable' => 'required|boolean',
            'password' => 'required|string',
        ]);
        if ($validator->fails()) {
            return[
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ];
        }

        return ['response' => 'success'
                    ,'enable' => $request->enable,
                    'password' => $request->password,
                ];
    }
}