<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Services\TwoFactorAuth\TwoFactorAuthService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use SellersHelper;

class TwoFactorAuthController extends Controller
{
    protected $twoFactorAuthService;
    protected $currentSeller;

    public function __construct(TwoFactorAuthService $twoFactorAuthService)
    {
        $this->twoFactorAuthService = $twoFactorAuthService;
        $this->currentSeller = SellersHelper::CurrentSeller();
    }

    /**
     * Generate a new 2FA secret and return QR code
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function setup(Request $request)
    {
        // Generate a new secret key
        $secretKey = $this->twoFactorAuthService->generateSecretKey($this->currentSeller);

        // Generate QR code
        $qrCodeUrl = $this->twoFactorAuthService->getQRCodeUrl($this->currentSeller, $secretKey);

        return response()->json([
            'response' => 'success',
            'message' => '2FA setup initiated',
            'result' => [
                'secret' => $secretKey,
                'qr_code' => $qrCodeUrl
            ]
        ]);
    }

    /**
     * Verify and enable 2FA
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function verify(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string|size:6',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
                'status' => 422
            ], 422);
        }

        // Verify the code
        if (!$this->twoFactorAuthService->verifyCode($request->code, $this->currentSeller)) {
            return response()->json([
                'response' => 'error',
                'message' => 'Invalid verification code',
                'status' => 422
            ], 422);
        }

        // Enable 2FA for the user
        $this->twoFactorAuthService->enable2FA($this->currentSeller);

        return response()->json([
            'response' => 'success',
            'message' => 'Two-factor authentication has been enabled',
        ]);
    }

    /**
     * Disable 2FA for the authenticated user
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function disable(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'code' => 'required|string|size:6',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
                'status' => 422
            ], 422);
        }

        // Verify the code before disabling 2FA
        if (!$this->twoFactorAuthService->verifyCode($request->code, $this->currentSeller)) {
            return response()->json([
                'response' => 'error',
                'message' => 'Invalid verification code',
                'status' => 422
            ], 422);
        }

        // Disable 2FA
        $this->twoFactorAuthService->disable2FA($this->currentSeller);

        return response()->json([
            'response' => 'success',
            'message' => 'Two-factor authentication has been disabled',
        ]);
    }

    /**
     * Get the current 2FA status
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function status()
    {
        return response()->json([
            'response' => 'success',
            'result' => [
                'enabled' => $this->twoFactorAuthService->is2FAEnabled($this->currentSeller)
            ]
        ]);
    }
}