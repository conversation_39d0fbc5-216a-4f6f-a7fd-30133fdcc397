<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SellersColis extends Model
{
	//
	protected $table = 'sellers_colis';

	public $timestamps = true;

	protected $fillable = [
		'code',
		'barecode',
		'price',
		'price_origine',
		'statut_colis',
		'statut_code',
		'statut_stock',
		'motif_statut',
		'first_date_motif',
		'date_motif_statut',
		'slugan_motif_statut',
		'time_reported',
		'last_call',
		'seller_id',
		'seller_name',
		'description',
		'page_id',
		'page_name',
		'recipient_name',
		'recipient_phone',
		'home_phone',
		'seconde_recipient_phone',
		'latitude',
		'longitude',
		'recipient_city',
		'recipient_sub_city',
		'khalij_district_id',
		'child_district_id',
		'recipient_address',
		'city_livraison',
		'region_id',
		'region_name',
		'codepostale',
		'from_stock',
		'price_livraison',
		'price_return',
		'price_firstmile',
		'custom_surcharge',
		'created_by_type',
		'api_name',
		'statut_validate',
		'collect_id',
		'collect_num',
		'picked_up_at',
		'picked_up_by',
		'transit_at',
		'shipped_at',
		'delivered_at',
		'undelivered_at',
		'paid_seller',
		'paid_seller_livraison',
		'facture_seller',
		'facture_seller_livraison',
		'facture_seller_upsell',
		'created_by',
		'updated_by',
		'deleted_by',
		'first_tracking_number',
		'tracking_number',
		'total_quantity',
		'total_weight',
		'volumetric_weight',
		'offer_upsell',
		'upsell',
		'upsell_by_id',
		'upsell_by_name',
		'is_validate',
		'validated_at',
		'validated_via',
		'validated_type',
		'price_upsell',
		'price_callcenter',
		'transport_agency',
		'paid_transport_agency',
		'paid_seller_confirmed',
		'paid_seller_upsell',
		'facture_seller_confirmed',
		'manager_shipping_id',
		'manager_shipping_name',
		'manager_callcenter_id',
		'manager_callcenter_name',
		'store_name',
		'currency',
		'product_ids',
		'products_quantities',
		'product_description',
		'product_varitante',
		'product_sku',
		'product_link',
		'comment_call_center',
		'comment_shipping',
		'from_city_id',
		'from_city_name',
		'order_num',
		'order_source',
		'shipping_by',
		'total_calls',
		'isexpired',
		'isclosed',
		'statut_lock',
		'suggestupsell',
		'shortaddress',
		'priority_callcenter',
		'priority_followup',
		'is_follow_up',
		'follow_up_problem',
		'feedbacktype',
		'feedback_updated',
		'feedback_created',
		'follow_up_totalcall',
		'follow_up_firstcall',
		'scheduledate',
		'areaname',
		'streetname',
		'housenumber',
		'nearestplace',
		'collected_at',
		'chatbot_statutsend',
		'chatbot_datesend',
		'order_type',
		'affiliate_id',
		'affiliate_offer',
		'payment_method',
		'prepaid_by',
		'reviews_followup',
		'deliveryman_id',
		'warehouse_id',
		'customer_language',
		'responsable_quality_id'
	];

	protected $guarded = [];

	public function deliveryman()
	{
		return $this->belongsTo(DeliveryMen::class, 'deliveryman_id');
	}
	public function deliveryWarehouse()
	{
		return $this->belongsTo(Warehouses::class, 'warehouse_id');
	}

	public function country()
	{
		return $this->belongsTo(Cities::class, 'city_livraison');
	}
    public function qualityAnswers()
    {
        return $this->hasMany(QualityAnswers::class,'order_id');
    }

		public function sellers_colis_products(){
		return $this->hasMany(SellersColisProducts::class, 'colis_id', 'id');
	}
}
