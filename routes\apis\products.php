<?php
use App\Http\Controllers\Warehousing\ProductsController;
use App\Http\Controllers\Warehousing\ProductsManagementController;
use App\Http\Controllers\Warehousing\ProductsOfferController;
use App\Http\Controllers\Warehousing\ProductsUpsellController;
use App\Http\Controllers\Warehousing\ProductsVariantController;
use Illuminate\Support\Facades\Route;

// Manage Products Routes
Route::prefix('products')->name('products.')->group(function () {
    Route::get('list', [ProductsController::class, 'list'])->name('list'); // List of products
    Route::get('types', [ProductsController::class, 'types'])->name('types'); // Types of products
    Route::get('categories', [ProductsController::class, 'categories'])->name('categories'); // Categories of products
    Route::get('{sku}', [ProductsController::class, 'details'])->name('details'); // Details of products
    Route::get('inventory/{sku}', [ProductsController::class, 'inventory'])->name('inventory'); // Inventory of products

    // Products Management
    Route::post('create', [ProductsManagementController::class, 'store'])->name('create'); // Create a new product
    Route::post('update/{id}', [ProductsManagementController::class, 'update'])->name('update'); // Update a product
    Route::put('archive/{id}', [ProductsManagementController::class, 'destroy'])->name('archive'); // Delete a product
    Route::put('unarchive/{id}', [ProductsManagementController::class, 'unarchive'])->name('unarchive');

    // Products Upsell
    Route::prefix('upsell')->name('upsell.')->group(function () {
        Route::post('create/{productId}', [ProductsUpsellController::class, 'store'])->name('store'); // Create a new product upsell
        Route::put('update/{productId}/{upsellId}', [ProductsUpsellController::class, 'update'])->name('update'); // Update a product upsell
        Route::delete('delete/{productId}/{upsellId}', [ProductsUpsellController::class, 'destroy'])->name('delete'); // Delete a product upsell
    });

    // Products sales price
    Route::prefix('offer')->name('offer.')->group(function () {
        Route::post('create/{productId}', [ProductsOfferController::class, 'store'])->name('store'); // Create a new product sales price
        Route::put('update/{productId}/{offreId}', [ProductsOfferController::class, 'update'])->name('update'); // Update a product sales price
        Route::delete('delete/{productId}/{offreId}', [ProductsOfferController::class, 'destroy'])->name('delete'); // Delete a product sales price
    });
    // Products variant
    Route::prefix('variant')->name('variant.')->group(function () {
        Route::post('create/{productId}', [ProductsVariantController::class, 'store'])->name('store'); // Create a new product sales price
        Route::put('update/{productId}/{variantId}', [ProductsVariantController::class, 'update'])->name('update'); // Update a product sales price
        Route::delete('delete/{productId}/{variantId}', [ProductsVariantController::class, 'destroy'])->name('delete'); // Delete a product sales price
        Route::post('create/{productId}/combine', [ProductsVariantController::class, 'storeWithCombination'])->name(name: 'combine');
    });

});