<?php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\SellersMetadata;
use App\Services\LeadSource\DropifyService;
use Symfony\Component\HttpFoundation\Response;

class CheckDropifyToken
{
    public function handle(Request $request, Closure $next)
    {
        // Get token from the header
        $token = $request->header('token');
        $client_id = $request->header('client-id');
        // Call Dropify service to validate token
        $dropifyService = app(DropifyService::class);
        $verifiedToken = $dropifyService->getClientId();
        if($verifiedToken != $client_id){

            return response()->json([
                'response' => 'error',
                'message'  => 'Unauthorized. You are not a seller.',
                'status'   => Response::HTTP_UNAUTHORIZED,
            ], Response::HTTP_UNAUTHORIZED);
        }

        if (!$token) {
            return response()->json([
                'error' => 'Token is missing from the header'
            ], 401);
        }

        // Check if the token exists in SellersMetadata
        $sellerMetadata = SellersMetadata::where('meta_data', 'dropify_seller_token')
                                         ->where('meta_value', $token)
                                         ->first();

        if (!$sellerMetadata) {
            return response()->json([
                'response' => 'error',
                'message'  => 'Unauthorized. You are not a seller.',
                'status'   => Response::HTTP_UNAUTHORIZED,
            ], Response::HTTP_UNAUTHORIZED);
        }

        // Add the seller_id to the request so it can be accessed later
        $request->headers->set('seller-id', $sellerMetadata->seller_id);

        // Token is valid, proceed
        return $next($request);
    }
}