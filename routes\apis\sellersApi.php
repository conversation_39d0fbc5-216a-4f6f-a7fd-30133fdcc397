<?php

use App\Http\Controllers\Orders\OrderController;
use Illuminate\Support\Facades\Route;

Route::middleware('throttle:60,1')->group(function () {
    // Orders
    Route::get('/orders', [OrderController::class, 'list'])->middleware('seller.api:orders:read');
    // Route::post('/orders', [OrderController::class,'store'])->middleware('seller.api:orders:create');
    // Route::post('/orders/status', [OrderController::class,'status'])->middleware('seller.api:orders:read');
    // Route::post('/orders/followup/cancel', [FollowupCtl::class,'cancel'])
    //     ->middleware('seller.api:orders:followup:cancel');

    // // Products
    // Route::post('/products', [ProductCtl::class,'store'])->middleware('seller.api:products:create');
    // Route::put('/products/{id}', [ProductCtl::class,'update'])->middleware('seller.api:products:update');

    // // Reference (you can collapse into ref:read)
    // Route::get('/call-center/statuses', [RefCtl::class,'ccStatuses'])->middleware('seller.api:ref:read');
    // Route::get('/call-center/cancellation-reasons', [RefCtl::class,'ccCancelReasons'])->middleware('seller.api:ref:read');
    // Route::get('/followup/statuses', [RefCtl::class,'fuStatuses'])->middleware('seller.api:ref:read');
    // Route::get('/followup/cancellation-reasons', [RefCtl::class,'fuCancelReasons'])->middleware('seller.api:ref:read');
});