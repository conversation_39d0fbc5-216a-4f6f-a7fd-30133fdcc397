<?php

namespace App\Services\TwoFactorAuth;

use App\Models\Sellers;
use App\Models\User;
use Illuminate\Http\Request;
use PragmaRX\Google2FA\Google2FA;
use PragmaRX\Google2FAQRCode\Google2FA as Google2FAQRCode;

class TwoFactorAuthService
{
    protected $google2fa;
    protected $google2faQR;

    public function __construct()
    {
        $this->google2fa = new Google2FA();
        $this->google2faQR = new Google2FAQRCode();
    }

    /**
     * Generate a new secret key for the user
     *
     * @param Sellers $seller
     * @return string
     */
    public function generateSecretKey(Sellers $seller): string
    {

        $secretKey = $this->google2fa->generateSecretKey();

        // Store the secret key in the database
        $seller->two_factor_secret = $secretKey;
        $seller->save();
        return $secretKey;
    }

    /**
     * Generate a QR code for the user to scan with Google Authenticator
     *
     * @param Sellers $seller
     * @param string $secretKey
     * @return string
     */
    public function getQRCodeUrl(Sellers $seller, string $secretKey): string
    {
        return $this->google2faQR->getQRCodeInline(
            config('app.name'),
            $seller->email ?? $seller->email_pro ?? 'seller-' . $seller->id,
            $secretKey
        );
    }

    /**
     * Verify the provided OTP code
     *
     * @param string $code
     * @param Sellers $seller
     * @return bool
     */
    public function verifyCode(string $code, Sellers $seller): bool
    {
        if (empty($seller->two_factor_secret)) {
            return false;
        }

        return $this->google2fa->verifyKey($seller->two_factor_secret, $code);
    }

    /**
     * Enable 2FA for a user
     *
     * @param Sellers $seller
     * @return void
     */
    public function enable2FA(Sellers $seller): void
    {
        $seller->two_factor_enabled = true;
        $seller->save();
    }

    /**
     * Disable 2FA for a user
     *
     * @param Sellers $seller
     * @return void
     */
    public function disable2FA(Sellers $seller): void
    {
        $seller->two_factor_enabled = false;
        $seller->two_factor_secret = null;
        $seller->save();
    }

    /**
     * Check if 2FA is enabled for a user
     *
     * @param Sellers $seller
     * @return bool
     */
    public function is2FAEnabled(Sellers $seller): bool
    {
        return $seller->two_factor_enabled;
    }
}