<?php

namespace App\Services\TwoFactorAuth;

use App\Models\Sellers;
use App\Models\User;
use Illuminate\Http\Request;
use PragmaRX\Google2FA\Google2FA;

class TwoFactorAuthService
{
    protected $google2fa;

    public function __construct()
    {
        $this->google2fa = new Google2FA();
    }

    /**
     * Set Seller Secret Key
     *
     * @param Sellers $seller
     */
    public function SetSellerSecretKey(Sellers $seller, $secretKey)
    {
        $seller->two_factor_enabled = true;
        // Store the secret key in the database
        $seller->two_factor_secret = $secretKey;
        $seller->save();
    }

     /**
     * Generate a new secret key for the user
     *
     * @param Sellers $seller
     * @return string
     */
    public function generateSecretKey(){
        return $this->google2fa->generateSecretKey();
    }

    /**
     * Generate a QR code URL for the user to scan with Google Authenticator
     *
     * @param Sellers $seller
     * @param string $secretKey
     * @return string
     */
    public function getQRCodeUrl(Sellers $seller, string $secretKey): string
    {
        $userEmail = $seller->email ?? $seller->email_pro ?? "seller-{$seller->id}";

        return $this->google2fa->getQRCodeUrl(
            'Power Group',
            $userEmail,
            $secretKey
        );
    }

    /**
     * Verify the provided OTP code
     *
     * @param string $code
     * @param Sellers $seller
     * @return array
     */
    public function verifyCode(string $code, Sellers $seller)
    {
        if (empty($seller->two_factor_secret)) {
            return [
                'response' => 'error',
                'message' => '2FA is not enabled for this user',
            ];
        }
        return $this->VerifyOTP($seller->two_factor_secret, $code);
    }

    /**
     * Verify the provided OTP code
     *
     * @param string $code
     * @param string $secret
     * @return array
     */
    public function VerifyOTP($secret, $code){
        if($this->google2fa->verifyKey($secret, $code)){
            return [
                'response' => 'success',
                'message' => 'Code verified successfully',
        ];
    }else{
        return [
            'response' => 'error',
            'message' => 'Invalid verification code',
        ];
    }
    }

    /**
     * Enable 2FA for a user
     *
     * @param Sellers $seller
     * @return void
     */
    public function enable2FA(Sellers $seller, $secretKey): void
    {
        $seller->two_factor_enabled = true;
        $seller->two_factor_secret = $secretKey;
        $seller->save();
    }

    /**
     * Disable 2FA for a user
     *
     * @param Sellers $seller
     * @return void
     */
    public function disable2FA(Sellers $seller): void
    {
        $seller->two_factor_enabled = false;
        $seller->two_factor_secret = null;
        $seller->save();
    }

    /**
     * Check if 2FA is enabled for a user
     *
     * @param Sellers $seller
     * @return bool
     */
    public function is2FAEnabled(Sellers $seller): bool
    {
        return is_null($seller->two_factor_enabled) ? false : $seller->two_factor_enabled;
    }
}