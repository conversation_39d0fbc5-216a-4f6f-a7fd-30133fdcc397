<?php

namespace App\Services\LeadSource;

use Illuminate\Http\Request;
use UsersHelper;
use Illuminate\Support\Facades\Http;

class DropifyService
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        // Any necessary initialization can go here.
    }

    /**
     * getClientId
     */
    public function getClientId()
    {
        return "x8Ux87HqpF0wBSBSVkVcpcrw2jpLsKGxsId3et8";
    }


    /**
     * Validate the request parameters.
     *
     * @param Request $request
     * @param array $requiredParams
     * @return \Illuminate\Http\JsonResponse
     */
    public function validateRequestParameters(Request $request, array $requiredParams)
    {
        $validatedParams = [];
        $missingParams = [];

        foreach ($requiredParams as $param) {
            $value = $request->query($param);

            if (!$value) {
                $missingParams[] = $param;
                continue;
            }

            $validatedParams[$param] = $value;
        }

        if (!empty($missingParams)) {
            return response()->json([
                'response' => 'error',
                'message' => __('sourcing.missing_required_parameters'),
                'missing_params' => $missingParams,
            ], 400);
        }

        return response()->json([
            'response' => 'success',
            'result' => $validatedParams,
        ]);
    }

    public function isValidDropifySignature($options=[],$isBased64 = false)
    {
        // Get params
        $client_id = $isBased64 ? base64_decode($options['client_id'] ?? '') : $options['client_id'] ?? NULL;
        $redirect_uri = $isBased64 ? base64_decode($options['redirect_uri'] ?? '') : $options['redirect_uri'] ?? NULL;

        // Check Params
        if ($client_id != $this->getClientId() || !filter_var($redirect_uri, FILTER_VALIDATE_URL)) {

            return response()->json([
                'response' => 'error',
                'message' => __('sourcing.request_not_authorized'), // hacker possibility here
            ], 401);

        }
        else
        {
            return true;
        }
    }


}