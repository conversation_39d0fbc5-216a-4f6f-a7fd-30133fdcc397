<?php

namespace App\Http\Controllers\Sourcing;

use App\Http\Controllers\Controller;
use App\Models\Medias;
use App\Models\SourcingProductVariant;
use App\Models\SourcingRequest;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use App\Services\Fields\FieldsService;
use App\Services\Validator\SourcingValidator;
use App\Services\Sourcing\SourcingJournalService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Auth;
use SellersHelper;
use App\Models\SourcingProduct;
use MediaHelper;
use App\Services\Sourcing\FilterSourcing;

class SourcingManagementController extends Controller
{
    protected SourcingValidator $sourcingValidator;
    protected FieldsService $FieldsService;
    protected SourcingJournalService $journalService;
    protected FilterSourcing $filterSourcing;
    protected $currentSeller;



    public function __construct()
    {
        $this->sourcingValidator = new SourcingValidator();
        $this->FieldsService = new FieldsService();
        $this->journalService = new SourcingJournalService();
        $this->filterSourcing = new FilterSourcing();

        // Current Seller
        $this->currentSeller = SellersHelper::CurrentSeller();
    }


    /**
     * List sourcing requests with pagination
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse
    {
        // Get the filtered results
        $result = $this->filterSourcing->getSourcingRequests($request->all());

        // Prepare the base response
        $results = [
            'response' => 'success',
            'result' => $request->count ? $result : ($result['items'] ?? []),
        ];

        // Include pagination details only if count is not requested
        if (!$request->count) {
            $results['paginate'] = [
                'count' => $result['count'] ?? 0,
                'currentPage' => $result['currentPage'] ?? 1,
                'totalPages' => $result['totalPages'] ?? 1,
            ];
        }

        // Return the response as a JSON object
        return response()->json($results);
    }

    /**
     * Create a new sourcing request.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function store(Request $request): JsonResponse
    {
        // Validate Data
        $resultValidator = $this->FieldsService->validateData([
            'type' => 'sourcingRequest',
            'option' => 'createValidation',
            'data' => $request->all(),
        ]);

        if ($resultValidator['response'] == "error") {
            return response()->json($resultValidator, 400);
        }

        // Process data
        $processData = $this->FieldsService->ProcessData([
            'option' => 'store',
            'type' => 'sourcingRequest',
            'data' => $request->all(),
            'AppendParams' => [
                'seller_id' => $this->currentSeller->id,
                'seller_name' => $this->currentSeller->fullname,
                'created_by' => $this->currentSeller->id,
                'request_code' => $this->generateUniqueRequestCode(),
                'status'=> 'placed',
            ]
        ]);

        // Create the sourcing request
        $sourcingRequest = SourcingRequest::create($processData);

        // add offer Prices
        if ($request->has('products')) {
            $products = json_decode($request->products, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $this->addProducts($products, $sourcingRequest);
            } else {
                return response()->json([
                    'response' => 'error',
                    'message' => 'Invalid products JSON format'
                ], 400);
            }
        }

        // Log the creation
        $this->journalService->logCreation($sourcingRequest);

        // Return the response with the created sourcing request
        return response()->json([
            'response' => 'success',
            'message' => __('sourcing.request_created_successfully')
        ], 201);
    }

    /**
     * Update an existing sourcing request.
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function update(Request $request, $id): JsonResponse
    {

        // Validate Sourcing Request
        $validateRequest = $this->sourcingValidator->validateRequest([
            'id' => $id,
            'layout' => 'details',
            'seller_id' => $this->currentSeller->id
        ]);
        if ($validateRequest['response'] == 'error') {
            return response()->json($validateRequest, 404);
        }

        // Get the sourcing request from validation result
        $sourcingRequest = $validateRequest['result'];

        // Validate Data
        $resultValidator = $this->FieldsService->validateData([
            'type' => 'sourcingRequest',
            'option' => 'updateValidation',
            'data' => $request->all(),
            'replaceParams' => [
                "{id}" => $id
            ]
        ]);

        if ($resultValidator['response'] == "error") {
            return response()->json($resultValidator, 400);
        }

        // Process data
        $processData = $this->FieldsService->ProcessData([
            'option' => 'update',
            'type' => 'sourcingRequest',
            'data' => $request->all(),
            'AppendParams' => [
                'updated_by' => $this->currentSeller->id,
            ]
        ]);

        // Update the sourcing request
        $sourcingRequest->update($processData);
        // Handle products update if present
        if ($request->has('products')) {
            $products = json_decode($request->products, true);
            if (json_last_error() === JSON_ERROR_NONE) {
                $this->addProducts($products, $sourcingRequest, true);
            } else {
                return response()->json([
                    'response' => 'error',
                    'message' => 'Invalid products JSON format'
                ], 400);
            }
        }
        // Log the update
        $this->journalService->logUpdate($sourcingRequest, 'updated');

        // Return the response with the updated sourcing request
        return response()->json([
            'response' => 'success',
            'message' => __('sourcing.request_updated_successfully')
        ], 200);
    }

    /**
     * Get sourcing request details
     */
    public function sourcingDetails( $id)
    {
        $sourcingRequest = SourcingRequest::with(['products'])
            ->where('id', $id)
            ->first();

        if (!$sourcingRequest) {
            return null;
        }

        // Add journal entries to the response
        //$sourcingRequest->journal_entries = $this->journalService->getEntriesForRequest($id);

        return $sourcingRequest;
    }

    /**
     * Delete a sourcing request.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function destroy($id): JsonResponse
    {
        // Validate Sourcing Request
        $validateRequest = $this->sourcingValidator->validateRequest([
            'id' => $id,
            'layout' => 'details',
            'seller_id' => $this->currentSeller->id
        ]);
        if ($validateRequest['response'] == 'error') {
            return response()->json($validateRequest, 404);
        }

        // Get the sourcing request from validation result
        $sourcingRequest = $validateRequest['result'];

        // Only allow abandon if status is 'placed' or 'unverified'
        if (!in_array($sourcingRequest->status, ['placed', 'unverified'])) {
            return response()->json([
                'response' => 'error',
                'message' => __('sourcing.request_cannot_be_abandoned')
            ], 400);
        }

        // Soft delete by updating status
        $sourcingRequest->update(['status' => 'abandoned']);

        // Log the deletion
        $this->journalService->logUpdate($sourcingRequest, 'abandoned');

        // Return Response
        return response()->json([
            'response' => 'success',
            'message' => __('sourcing.request_deleted_successfully'),
        ]);
    }

    /**
     * Restore a deleted sourcing request.
     *
     * @param int $id
     * @return JsonResponse
     */
    public function restore($id): JsonResponse
    {
        // Validate Sourcing Request
        $validateRequest = $this->sourcingValidator->validateRequest(['id' => $id, 'layout' => 'details', 'status' => 'abandoned']);
        if ($validateRequest['response'] == 'error') {
            return response()->json($validateRequest, 404);
        }

        // Find the sourcing request
        $sourcingRequest = SourcingRequest::find($id);
        $sourcingRequest->update(['status' => 'placed']);

        // Log the restoration
        $this->journalService->logUpdate($sourcingRequest, 'restored');

        // Return Response
        return response()->json([
            'response' => 'success',
            'message' => __('sourcing.request_restored_successfully'),
        ]);
    }

     /**
     * List of status
     */
    public function statuses(){
        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $this->filterSourcing->SourcingRequestStatus()
        ]);
    }


    /**
     * Collect old media files associated with the sourcing request’s products.
     *
     * @param SourcingRequest $sourcingRequest
     * @return array<int, Medias> Array of media objects keyed by old product ID
     */
    private function collectOldMedias(SourcingRequest $sourcingRequest): array
    {
        $oldMediasByProduct = [];
        $existingProducts = SourcingProduct::where('sourcing_request_id', $sourcingRequest->id)->get();

        foreach ($existingProducts as $existingProduct) {
            // Get media with image_slug 'image' related to the product
            $media = Medias::where('table_name', 'seller_sourcing_product')
                ->where('table_id', $existingProduct->id)
                ->where('image_slug', 'image')
                ->first();

            if ($media) {
                $oldMediasByProduct[$existingProduct->id] = $media;
            }
        }

        return $oldMediasByProduct;
    }

    /**
     * Delete all variants and products associated with the sourcing request.
     * Does NOT delete media files to allow re-assignment.
     *
     * @param SourcingRequest $sourcingRequest
     * @return void
     */
    private function deleteOldProductsAndVariants(SourcingRequest $sourcingRequest): void
    {
        $existingProducts = SourcingProduct::where('sourcing_request_id', $sourcingRequest->id)->get();

        foreach ($existingProducts as $existingProduct) {
            // Delete all variants of the product
            foreach ($existingProduct->variants as $variant) {
                $variant->delete();
            }
            // Delete the product itself
            $existingProduct->delete();
        }
    }

    /**
     * Assign an existing media record to the new product or upload a new image file.
     *
     * @param array $product The product data from request
     * @param int $index Index of the product in the request array
     * @param SourcingProduct $mainProduct Newly created product model
     * @param array<int, Medias> $oldMediasByProduct Old media keyed by old product id
     * @return void
     */
    private function assignOrUploadProductImage($product, $index, $mainProduct, array $oldMediasByProduct): void
    {
        if (request()->hasFile("product_{$index}_image")) {
            // New image file uploaded, save it
            $fileKey = "product_{$index}_image";
            $this->saveSourcingProductImage(request(), $mainProduct->id, $fileKey);
        } elseif (isset($product['imageUrl']) && !empty($product['imageUrl'])) {
            // Reuse old media by matching filename from imageUrl
            $parsedUrl = parse_url($product['imageUrl']);
            $fileName = basename($parsedUrl['path']);

            $oldMedia = null;
            foreach ($oldMediasByProduct as $media) {
                if ($media->media === $fileName) {
                    $oldMedia = $media;
                    break;
                }
            }

            if ($oldMedia) {
                // Update media to link it with the new product ID
                $oldMedia->update([
                    'table_id' => $mainProduct->id,
                    'updated_at' => now(),
                ]);
            }
        }
    }

    /**
     * Add products and their variants to a sourcing request.
     * If $deleteBefore is true, existing products and variants will be deleted first.
     * Handles image upload or re-assignment based on the presence of file or imageUrl.
     *
     * @param array $products Array of product data arrays
     * @param SourcingRequest $sourcingRequest The sourcing request model
     * @param bool $deleteBefore Whether to delete old products before adding new ones
     * @return void
     */
    private function addProducts(array $products, SourcingRequest $sourcingRequest, bool $deleteBefore = false): void
    {
        $oldMediasByProduct = [];

        if ($deleteBefore) {
            // Step 1: Collect old media associated with existing products
            $oldMediasByProduct = $this->collectOldMedias($sourcingRequest);

            // Step 2: Delete old variants and products (media retained for reassignment)
            $this->deleteOldProductsAndVariants($sourcingRequest);
        }

        // Step 3: Process each new product
        foreach ($products as $index => $product) {
            $hasVariants = isset($product['variants']) && !empty($product['variants']);

            // Process product data through FieldsService before saving
            $processProductData = $this->FieldsService->ProcessData([
                'option' => 'store',
                'type' => 'sourcingProduct',
                'data' => $product,
                'AppendParams' => [
                    'seller_id' => $this->currentSeller->id,
                    'seller_name' => $this->currentSeller->fullname,
                    'sourcing_request_id'=> $sourcingRequest->id,
                    'sourcing_request_code'=> $sourcingRequest->request_code,
                    'created_by' => $this->currentSeller->id,
                ]
            ]);

            // Create the product record
            $mainProduct = SourcingProduct::create($processProductData);

            // Step 4: Handle product image (upload new or reassign old media)
            $this->assignOrUploadProductImage($product, $index, $mainProduct, $oldMediasByProduct);

            // Step 5: Add variants if present
            if ($hasVariants) {
                $this->addVariants($product['variants'], $mainProduct, $sourcingRequest);
            }
        }
    }


    /**
     * Add variants to a product
     *
     * @param array $variants
     * @param SourcingProduct $mainProduct
     * @param SourcingRequest $sourcingRequest
     * @return void
     */
    private function addVariants(array $variants, SourcingProduct $mainProduct, SourcingRequest $sourcingRequest): void
    {
        foreach ($variants as $variant) {
            // Check if name and value exist
            if (!isset($variant['name']) || !isset($variant['value'])) {
                continue; // Skip invalid variant
            }

            // Process variant data
            $processVariantData = $this->FieldsService->ProcessData([
                'option' => 'store',
                'type' => 'sourcingProductVariant',
                'data' => $variant,
                'AppendParams' => [
                    'product_id' => $mainProduct->id,
                    'sourcing_request_id' => $sourcingRequest->id,
                    'sourcing_request_code' => $sourcingRequest->request_code,
                    'seller_id' => $this->currentSeller->id,
                    'seller_name' => $this->currentSeller->fullname,
                    'created_by' => $this->currentSeller->id,
                ]
            ]);

            // Store the variant using the SourcingProductVariant model
            SourcingProductVariant::create($processVariantData);
        }
    }

    /**
     * saveProductImage
     */
    private function saveSourcingProductImage($request, $productId, $fileKey){
        MediaHelper::saveMedia([
            'request' => $request,
            'imageSlug' => "image",
            'fileName' => $fileKey,  // use dynamic key here!
            'tableName' => 'seller_sourcing_product',
            'tableId' => $productId,
        ]);
    }



    /**
     * Generate a unique request code that doesn't exist in the database
     *
     * @return string
     */
    private function generateUniqueRequestCode(): string
    {
        do {
            $code = strtoupper(substr(md5(uniqid()), 0, 10));
        } while (SourcingRequest::where('request_code', $code)->exists());

        return $code;
    }

    /**
     * Get all products from seller_sourcing_product table
     *
     * @return JsonResponse
     */
    public function getRequestWithProducts($id): JsonResponse
    {
        try {
            // Fetch and format the sourcing request using FilterSourcing
            $response = $this->filterSourcing->getFormattedRequestWithProducts($id);

            if (!$response) {
                return response()->json([
                    'response' => 'error',
                    'message' => 'Sourcing request not found'
                ], 404);
            }

            // Return the response
            return response()->json([
                'response' => 'success',
                'result' => $response
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'response' => 'error',
                'message' => 'Failed to fetch request and products: ' . $e->getMessage()
            ], 500);
        }
    }



}