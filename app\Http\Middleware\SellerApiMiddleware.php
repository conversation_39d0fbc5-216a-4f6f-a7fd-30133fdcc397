<?php
namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Symfony\Component\HttpFoundation\Response;
use App\Models\Sellers;

class SellerApiMiddleware
{
    public function handle(Request $request, Closure $next, ...$requiredScopes)
    {
        $bearer = $request->bearerToken();
        if (!$bearer) {
            return $this->unauthorized('Missing bearer token');
        }

        // 1) Decrypt + parse payload (validates signature/MAC)
        try {
            $payload = json_decode(Crypt::decryptString($bearer), true, 512, JSON_THROW_ON_ERROR);
        } catch (\Throwable $e) {
            return $this->unauthorized('Invalid token');
        }

        // 2) Basic payload checks
        if (!isset($payload['sid'], $payload['scopes'])) {
            return $this->unauthorized('Malformed token');
        }
        if (!empty($payload['exp']) && time() > (int)$payload['exp']) {
            return $this->unauthorized('Token expired');
        }

        // 3) Load seller & ensure token matches what's on file (revocation check)
        $seller = Sellers::find($payload['sid']);
        if (!$seller || !hash_equals((string)$seller->api_token, (string)$bearer)) {
            return $this->unauthorized('Token revoked or seller not found');
        }

        // 4) Scope enforcement
        $granted = collect($payload['scopes']);
        foreach ($requiredScopes as $scope) {
            if (!$granted->contains($scope)) {
                return response()->json([
                    'error' => ['code' => 'insufficient_scope', 'message' => "Missing scope: {$scope}"]
                ], Response::HTTP_FORBIDDEN);
            }
        }

        //params formatter for getOrders
        $this->formatParams($request);

        app()->instance('currentSeller', $seller);

        return $next($request);
    }

    private function unauthorized(string $msg)
    {
        return response()->json(['error' => ['code' => 'unauthorized', 'message' => $msg]], 401);
    }

    private function formatParams(Request $request)
    {


    }
}