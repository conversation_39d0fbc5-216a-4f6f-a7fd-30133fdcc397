<?php

namespace App\Http\Controllers\LeadSources;

use App\Http\Controllers\Controller;
use App\Models\Sellers;
use App\Services\LeadSource\FilterLeadSource;
use App\Services\LeadSource\GoogleSheetsService;
use App\Services\LeadSource\LeadSourceService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Google\Service\Sheets;
use Google\Service\Drive;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use PgSql\Lob;

class GoogleSheetsController extends Controller{
    /**
     * constructor.
     */
    protected GoogleSheetsService $GoogleSheetsService;
    protected LeadSourceService $LeadSourceService;
    public function __construct(){
        // Instant Services
        $this->GoogleSheetsService = new GoogleSheetsService();
        $this->LeadSourceService = new LeadSourceService();
    }

    /**
     * setup Google Sheets
     */
    public function setup(Request $request): JsonResponse{
        // Get the API client and construct the service object.
        $responseGoogle = $this->GoogleSheetsService->loadGoogleClient();

        // Initial params
        $response = $responseGoogle['response'] ?? null;
        $actionType = $responseGoogle['actionType'] ?? null;
        $client = $responseGoogle['client'] ?? null;


        // Check if exist action to make it
        if($response == "success" && !$actionType){
            try {
                // Create Drive service objects
                $driveService = new Drive($client);

                // Get the list of spreadsheet files
                $sheetsList = $driveService->files->listFiles([
                    'q' => "mimeType='application/vnd.google-apps.spreadsheet'",
                    'fields' => 'nextPageToken, files(id, name)',
                ]);
                // Prepare list of sheets
                $listesSpreadsheet = array_map(fn($file) => [
                    'id' => $file->getId(),
                    'name' => $file->getName(),
                ], $sheetsList->getFiles() ?? []);

                // Success Response
                return response()->json([
                    'response' => 'success',
                    'actionType' => 'spreadSheet',
                    'result' => $listesSpreadsheet,
                ]);
            } catch (\Exception $e) {
                // Error handling
                return response()->json([
                    'response' => 'error',
                    'message' => 'Error loading sheets: ' . $e->getMessage(),
                ]);
            }
        }

        // Return Default Respose
        return response()->json($responseGoogle);
    }

    /**
     * Display a listing of the sheets.
     *
     * @return \Illuminate\Http\Response
     */
    public function sheets(Request $request): JsonResponse{
        // Retrieve 'spreadsheetId'
        $spreadsheetId = $request->input('spreadsheetId');

        // Get the API client and construct the service object.
        $responseGoogle = $this->GoogleSheetsService->loadGoogleClient();

        // Check reponse
        if($responseGoogle['response'] == "success"){
            try{
                // Instance client
                $client = $responseGoogle['client'];
                $service = new Sheets($client);

                // Liste Sheet
                $spreadSheet = $service->spreadsheets->get($spreadsheetId);
                $sheets = $spreadSheet->getSheets();

                // Prepare list of sheet titles
                $listesSheet = array_map(fn($sheet) => $sheet->properties->title, $sheets);

                // Success Response
                return response()->json([
                    'response' => 'success',
                    'result' => $listesSheet,
                ]);
            } catch (\Exception $e) {
                // Error handling
                return response()->json([
                    'response' => 'error',
                    'message' => 'Error loading sheets: ' . $e->getMessage(),
                ]);
            }
        }


        // Return Default Respose
        return response()->json($responseGoogle);
    }

   /**
     * Install Google Sheet Integration
     */
    public function install(Request $request): JsonResponse{
        $data = $request->all();
        $spreadsheetId = $data['spreadsheetId'] ?? null;
        $sheetName = $data['sheetName'] ?? null;

        // Validate if both parameters are present
        $validation = $this->validateInstallGoogleSheet($request);

        if ($validation['response'] == "error") { return response()->json($validation, 400); }

        try {
            // Get Sheet Name
            //$sheetName = $this->loadSheetName($spreadsheetId); /** to delete after */

            // Create Leads Source Auto Import
            $rowAutoImport = $this->LeadSourceService->createLeadsSourceAutoImport([
                'spreadsheetId' => $spreadsheetId,
                'sheetName' => $sheetName,
                'apiType' => 'google_sheets',
            ]);

            // Run Import Google Sheet
            Http::get(env('URL_ERP').'/scheduled-tasks/googlesheet:importorders', [
                'file_id' => $rowAutoImport->id,
            ]);


            // Return success response
            return response()->json([
                'response' => 'success',
            ]);
        } catch (\Exception $e) {
            // Handle any exceptions that may occur
            return response()->json([
                'response' => 'error',
                'message' => 'An error occurred while installing the Google Sheet.',
                'details' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * load Sheet Name
     */
    private function loadSheetName($spreadsheetId){
        // Get the API client and construct the service object.
        $responseGoogle = $this->GoogleSheetsService->loadGoogleClient();

        // Check reponse
        if($responseGoogle['response'] == "success"){
            try{
                // Instance client
                $client = $responseGoogle['client'];
                $service = new Sheets($client);

                // Liste Sheet
                $spreadSheet = $service->spreadsheets->get($spreadsheetId);
                $sheets = $spreadSheet->getSheets();

                // Prepare list of sheet titles
                $listesSheet = array_map(fn($sheet) => $sheet->properties->title, $sheets);

                // Return First Sheet
                return $listesSheet[0];
            } catch (\Exception $e) {

            }
        }
    }

    /**
     * Delete Google Sheet Account
     */
    public function delete(Request $request): JsonResponse {
        // Delete the linked LeadsSource account
        $deleteResponse = $this->LeadSourceService->deleteLeadsSourceAccount([
            'api_name' => "google-sheets",
        ]);

        if ($deleteResponse['response'] === 'success') {
            return response()->json($deleteResponse, 200);

        }

        return response()->json($deleteResponse, 400);
    }

    /*
       Google Sheet Callback
    */
    public function oauth2callback(Request $request){
        // Retrieve the authorization code from the request
        $authCode = $request->input('code');
        $state = json_decode($request->input('state'));
        $seller_id = $state->seller_id ?? null;

        try {

            // Instance the Google client
            $client = $this->GoogleSheetsService->createGoogleClient();

            // Exchange the authorization code for an access token
            $accessToken = $client->fetchAccessTokenWithAuthCode($authCode);

            // Check if the access token was successfully retrieved
            if (!empty($accessToken['access_token'])) {
                // Set the access token in the client
                $client->setAccessToken($accessToken);

                // Get Seller
                $seller = Sellers::find($seller_id);

                // Store the token
                $tokenClient = json_encode($client->getAccessToken());

                // Insert token into the database via LeadSourceService
                $this->LeadSourceService->createLeadSourceToken([
                    'api_name' => 'google-sheets',
                    'api_token' => $tokenClient,
                    'seller' => $seller,
                ]);

                // Return a success response
                return Redirect::to(env('URL_APP_SELLERS').'/lead-sources?sheets=1');

            } else {
                // Return an error response if access token is missing
                return response()->json(['response' => 'success','message' => 'Failed to retrieve access token.'], 400);
            }
        } catch (\Exception $e) {
            return response()->json(['response' => 'success','message' => 'An error occurred while processing the request.'], 500);
        }
    }

    /*
       Import Now
    */
    public function import($id,Request $request){
        try {
            $response = Http::get(env('URL_ERP').'/scheduled-tasks/googlesheet:importorders', [
                'id' => $id,
            ]);


            if ($response->successful()) {
                return response()->json([
                    'response' => 'success',
                ]);
            }

            return response()->json([
                'response' => 'error',
                'message' => 'Failed to import from Google Sheet.',
                'details' => $response->body(),
            ], $response->status());

        } catch (\Exception $e) {
            return response()->json([
                'response' => 'error',
                'message' => 'An error occurred while importing the Google Sheet.',
                'details' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Validate Install Google Sheet
     */
    private function validateInstallGoogleSheet(Request $request): array|bool{
        $data = $request->all();

        if (empty($data['spreadsheetId']) || empty($data['sheetName'])) {
            return [
                'response' => 'error',
                'message' => 'Both spreadsheetId and sheetName are required.',
            ];
        }

        return [
            'response' => 'success',
        ];
    }


}