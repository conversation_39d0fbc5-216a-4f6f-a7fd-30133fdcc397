{"table": "sellers_stock", "fields": {"id": {"dbName": "id", "layout": "all,general,details,variantes"}, "sellerId": {"dbName": "seller_id", "layout": "all"}, "sellerName": {"dbName": "seller_name", "layout": "all"}, "name": {"dbName": "name", "layout": "all,general,details,variantes", "createValidation": "required|string|max:255", "updateValidation": "required|string|max:255"}, "arabicName": {"dbName": "arabic_name", "layout": "all,general,details"}, "sku": {"dbName": "reference", "layout": "all,general,details,variantes", "createValidation": "required|unique:sellers_stock,reference", "updateValidation": "required|unique:sellers_stock,reference,{id},id"}, "weight": {"dbName": "weight", "layout": "all,details"}, "width": {"dbName": "width", "layout": "all,details"}, "height": {"dbName": "height", "layout": "all,details"}, "length": {"dbName": "length", "layout": "all,details"}, "status": {"dbName": "statut", "layout": "all"}, "isArchive": {"dbName": "is_archive", "layout": "all,general,details"}, "descriptionCallcenter": {"dbName": "description_callcenter", "layout": "all,details"}, "productLink": {"dbName": "product_link", "layout": "all,general,details,variantes"}, "productVideo": {"dbName": "product_video", "layout": "all,general,details,variantes"}, "warehouseId": {"dbName": "warehouse_id", "layout": "all,general,details"}, "warehouseName": {"dbName": "warehouse_name", "layout": "all"}, "shippingPriceType": {"dbName": "shipping_price_type", "layout": "all"}, "shippingBy": {"dbName": "shipping_by", "layout": "all"}, "confirmedBy": {"dbName": "confirmed_by", "layout": "all"}, "confirmedAt": {"dbName": "confirmed_at", "layout": "all"}, "createdBy": {"dbName": "created_by", "layout": "all"}, "updatedBy": {"dbName": "updated_by", "layout": "all"}, "listeStock": {"dbName": "liste_stock", "layout": "all"}, "productType": {"dbName": "product_type", "layout": "all,general,details", "createValidation": "required|string|max:255", "updateValidation": "required|string|max:255"}, "shippingType": {"dbName": "type", "layout": "all,general,details"}, "quantity": {"dbName": "quantity", "layout": "all,general,details"}, "parent": {"dbName": "parent", "layout": "all,general,details"}, "hscode": {"dbName": "hscode", "layout": "all,details"}, "category": {"dbName": "categorie_name", "layout": "all,general,details", "createValidation": "required|string|max:255", "updateValidation": "required|string|max:255"}, "isTest": {"dbName": "is_test", "layout": "all"}, "statusChatbot": {"dbName": "statut_chatbot", "layout": "all"}, "declaredValue": {"dbName": "declared_value", "layout": "all"}, "mainImage": {"dbRAW": "'' AS mainImage", "layout": "all,general,details"}, "remainingQuantity": {"dbName": "remaining_quantity", "layout": "all"}}}