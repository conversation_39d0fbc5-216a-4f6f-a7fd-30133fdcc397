<?php

use App\Http\Controllers\LeadSources\LeadSourcesController;
use Illuminate\Support\Facades\Route;

Route::get('leadsources/list', [LeadSourcesController::class, 'list'])->name('leadsources/list')->middleware('auth.token'); // List of lead sources
// Delete lead source import
Route::delete('leadsources/delete', [LeadSourcesController::class, 'delete'])
->name('leadsources.delete')->middleware('auth.token');

Route::post('leadsources/fullfillment', [LeadSourcesController::class, 'fullfillment'])->name('fullfillment')->middleware('auth.server');
// Include integrations for different platforms
require_once 'lightfunnels.php'; // LightFunnels API integration
require_once 'shopify.php';      // Shopify API integration
require_once 'youcan.php';       // YouCan API integration
require_once 'google-sheets.php';       // Google Sheets API integration
require_once 'excel.php';       // Excel integration
require_once 'dropify.php';       // Excel integration
require_once 'woocommerce.php';       // WooCommerce integration