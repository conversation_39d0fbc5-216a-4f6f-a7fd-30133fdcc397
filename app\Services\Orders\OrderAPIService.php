<?php
namespace App\Services\Orders;

use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use <PERSON><PERSON>Helper as GlobalSellersHelper;

class OrderAPIService{
    /**
     *  Initialize the current seller using SellersHelper
     */
    protected $currentSeller;
    public function __construct(){
        $this->currentSeller = GlobalSellersHelper::CurrentSeller();
    }

    /**
     *  create an order via api
     */
    public function sendOrders($options = []){
        $orderData = $options['orderData'] ?? [];
        $sellerId = $options['sellerId'] ?? null;

        // Send the order request and handle response
        $response = $this->sendOrderRequest($orderData, $sellerId);
        
        // Check for errors in the response
        $errorResponse = $this->checkResponse($response);
        if ($errorResponse) {
            return $errorResponse;
        }
    }

    /**
     * send Order Request
     */
    private function sendOrderRequest($orderData, $sellerId = null){
        
        return Http::withHeaders([
            'Content-Type' => 'application/json',
            'Client' => env('ERP_API_CLIENT'),
            'ClientToken' => env('ERP_API_CLIENT_TOKEN'),
            'SellersReference' => $sellerId ?? $this->currentSeller->id, // id verification requiered here ??

        ])->post(env('URL_ERP') . '/api/orders/create', [
            'ListeOrders' => $orderData,
        ]);
    }

    /**
     * check Response
     */
    private function checkResponse($response){
        // Check if the response failed
        if ($response->failed()) {
            return [
                'response' => 'error',
                'message'  => 'Failed to communicate with the API.',
            ];
        }

        // Get the response as JSON
        $responseOrders = $response->json();
        
        // Find orders with an 'error' status
        $errors = array_filter($responseOrders, fn($order) => $order['statut'] === 'error');

        if (!empty($errors)) {
            // Concatenate error messages
            $errorMessages = implode(" ... ", array_map(fn($error) => $error['message'], $errors));

            return [
                'response' => 'error',
                'message'  => $errorMessages,
            ];
        }

        // Return Response 
        $firstRespone = $responseOrders[0] ?? null;
        unset($firstRespone['statut']);
        $firstRespone = array_merge(['response' => 'success'], $firstRespone);
        return $firstRespone;
    }

}