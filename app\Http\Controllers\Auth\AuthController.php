<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Mail\SendCodeResetPassword;
use App\Models\ResetCodePassword;
use App\Models\Sellers;
use App\Models\User;
use App\Services\Fields\FieldsService;
use App\Services\Notifications\MailService;
use App\Services\Sellers\FilterSellers;
use App\Services\TwoFactorAuth\TwoFactorAuthService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller{
    protected FilterSellers $FilterSellers;
    protected TwoFactorAuthService $twoFactorAuthService;
    protected MailService $mailService;

    /**
     * Construct
     */
    public function __construct(TwoFactorAuthService $twoFactorAuthService){
        $this->FilterSellers = new FilterSellers();
        $this->twoFactorAuthService = $twoFactorAuthService;
        $this->mailService = new MailService();
    }

    /**
     * Login
     */
    public function login(Request $request){
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'password' => 'required|string',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        // Retrieve user by email
        $user = User::where('email', $request->email)->where('type','seller')->first();

        // Check if user exists
        if (!$user || !Hash::check($request->password, $user->password)) {
            return response()->json([
                'response' => 'error',
                'message' => 'Invalid credentials'
            ], 401);
        }

        // Check if 2FA is enabled for this user
        $seller = $user->seller;
        /*if ($seller && $this->twoFactorAuthService->is2FAEnabled($seller)) {
            // Return a response indicating 2FA is required
            return response()->json([
                'response' => 'pending',
                'message' => 'Two-factor authentication required',
                'result' => [
                    'requires_2fa' => true,
                    'user_id' => $user->id
                ]
            ], 200);
        }*/

        // Generate Passport token
        $token = $user->createToken('token')->accessToken;

        return response()->json([
            'response' => 'success',
            'message' => 'Login successful',
            'result' => [
                'accessToken' => $token,
                'user' => $this->renderUserInfos($user),
            ],
        ], 200);
    }

    /**
     * Verify 2FA code and complete login
     */
    public function verify2FA(Request $request){
        $validator = Validator::make($request->all(), [
            'email' => 'required|email',
            'code' => 'required|string|size:6',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ], 422);
        }

        // Retrieve user by email
        $user = User::where('email', $request->email)->where('type','seller')->first();

        // Check if user exists
        if (!$user) {
            return response()->json([
                'response' => 'error',
                'message' => 'User not found'
            ], 404);
        }

        $seller = $user->seller;
        if (!$seller) {
            return response()->json([
                'response' => 'error',
                'message' => 'Seller profile not found'
            ], 404);
        }

        // Verify the 2FA code
        if (!$this->twoFactorAuthService->verifyCode($request->code, $seller)) {
            return response()->json([
                'response' => 'error',
                'message' => 'Invalid verification code'
            ], 422);
        }

        // Generate Passport token
        $token = $user->createToken('token')->accessToken;

        return response()->json([
            'response' => 'success',
            'message' => 'Login successful',
            'result' => [
                'accessToken' => $token,
                'user' => $this->renderUserInfos($user),
            ],
        ], 200);
    }

    /**
     * loginByID
     */
    public function loginByID($id,Request $request){
        // Retrieve user by email
        $rowSeller = Sellers::findOrFail($id);
        $user = User::findOrFail($rowSeller->user_id);

        // Generate Passport token
        $token = $user->createToken('token')->accessToken;

        return response()->json([
            'response' => 'success',
            'message' => 'Login successful',
            'result' => [
                'accessToken' => $token,
                'user' => $this->renderUserInfos($user),
            ],
        ], 200);
    }

    /**
     * Forgot Password
     */
    public function forgotPassword(Request $request)
    {
        $data = $request->validate([
            'email' => 'required|email',
        ]);
        // find user's email
        $user = User::firstWhere('email', $request->email);

        if (!$user) {
            return response()->json([
                'response' => 'error',
                'message' => 'User not found'
            ], 404);
        }

        // Delete all old code that the user sent before.
        ResetCodePassword::where('email', $request->email)->delete();

        // Generate random code
        $data['code'] = mt_rand(100000, 999999);

        // Create a new code
        $codeData = ResetCodePassword::create($data);
        $seller = Sellers::where('email', $request->email)->first();

        try {

            // Mail::to($request->email)->send(new SendCodeResetPassword($codeData->code,$seller->fullname ?? $request->email));

            // Prepare email content
            $emailData = [
                'toEmail' => $request->email,
                'subject' => 'Password Reset Code',
                'body' => "Hello {$seller->fullname},<br><br>Here is your password reset code: {$codeData->code}<br><br>Best regards,<br>The Power Group Company Team",
            ];

            $this->mailService->SendMail($emailData);
            // No need to check Mail::failures() here anymore

            return response()->json([
                'response' => 'success',
                'message' => 'Password reset code has been sent to your email.',
            ], 200);
        } catch (\Exception $e) {
            // Optional: Log the error for debugging

            return response()->json([
                'response' => 'error',
                'message' => $e->getMessage(),
            ], 500);
        }
    }


    public function checkResetCode(Request $request)
    {
        $request->validate([
            'code' => 'required|string|exists:reset_code_passwords',
            'email' => 'required|email',
        ]);

        // Find the reset code entry
        $passwordReset = ResetCodePassword::where('code', $request->code)
            ->where('email', $request->email)
            ->first();

        if (!$passwordReset) {
            return response()->json(['message' => 'Invalid reset code.'], 422);
        }

        // Check expiration (1 hour)
        if ($passwordReset->created_at->addHour()->isPast()) {
            $passwordReset->delete();
            return response()->json(['message' => 'Reset code has expired.'], 422);
        }

        // Return success response
        return response()->json(['message' => 'Reset code is valid.'], 200);
    }

    public function resetPassword(Request $request)
    {
        $request->validate([
            'code' => 'required|string|exists:reset_code_passwords',
            'email' => 'required|email',
            'password' => 'required|string|min:6',
        ]);

        // Find reset code entry
        $passwordReset = ResetCodePassword::where('code', $request->code)
        ->where('email', $request->email)
        ->first();

        if (!$passwordReset) {
            return response()->json(['message' => 'Invalid reset code.'], 422);
        }

        // (Optional) Re-check expiration here for safety
        if ($passwordReset->created_at->addHour()->isPast()) {
            $passwordReset->delete();
            return response()->json(['message' => 'Reset code has expired.'], 422);
        }

        // Find user by email
        $user = User::firstWhere('email', $passwordReset->email);

        if (!$user) {
            return response()->json(['message' => 'User not found.'], 404);
        }

        // Update password (hashing is automatic if using Laravel mutators)
        $user->password = bcrypt($request->password);
        $user->save();

        // Delete the used reset code
        $passwordReset->delete();

        return response()->json(['message' => 'Password has been successfully reset.'], 200);
    }



    /**
     * Logout the authenticated user by revoking their access token.
     */
    public function logout(Request $request)
    {
       // Use auth('api') instead of $request->user()
        $user = auth('api')->user();

        if (!$user) {
            return response()->json([
                'response' => 'error',
                'message'  => 'Unauthorized. User not authenticated.',
                'status'   => 401,
            ], 401);
        }

        // Revoke the user's token
        $user->token()->revoke();

        return response()->json([
            'response' => 'success',
            'message' => 'Logout successful',
        ], 200);
    }

    /**
     * render User Infos
     */
    private function renderUserInfos($user){
        // Retrieve the seller record
        return $this->FilterSellers->getSellers([
            "user_id" => $user->id,
            "first" => 1,
        ]);
    }
}