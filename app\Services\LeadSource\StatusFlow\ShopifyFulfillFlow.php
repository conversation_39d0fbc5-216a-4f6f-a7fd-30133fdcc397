<?php

namespace App\Services\LeadSource\StatusFlow;

use App\Enums\CodStatus;
use App\Models\SellersApiImports;
use App\Services\Orders\OrderStatusService;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;

class ShopifyFulfillFlow
{
    /** Shopify API version */
    private string $apiVersion = '2025-07';
    protected $statusService;

    public function __construct(
        private ?string $metafieldNamespace = 'power_group_world',
        private ?string $metafieldKey = 'status',
        private ?string $metafieldType = 'single_line_text_field',
    ) {
        $this->statusService = new OrderStatusService();
    }

    public function syncStatusBySeller(int $sellerId, int $orderId, string $statusName, array $ctx = []): array
    {
        // Ensure the given statusName is valid
        $validNames = $this->statusService->mapStandardStatuses(['getList' => true]);
        if (!isset($validNames[$statusName])) {
            return [
                'response' => 'error',
                'message'  => 'Unknown status',
                'status'   => 422,
                'details'  => ['incoming' => $statusName]
            ];
        }

        // Get Shopify token and url
        $source = SellersApiImports::where('api_name','shopify')
            ->where('seller_id',$sellerId)->first();

        if (!$source) {
            return ['response'=>'error','message'=>'Shopify source not found for seller','status'=>404];
        }

        $shop  = $source->shopurl;
        $token = $source->api_token;

        // Human label for tag
        $label = $validNames[$statusName];

        // Route by canonical "name"
        switch ($statusName) {
            // --- Call-center: tag + metafield only
            case 'newlead':
            case 'confirmed':
            case 'noanswer':
            case 'schedule':
            case 'doubleorder':
            case 'pending':
            case 'wrongphonenumber':
            case 'test':
                return $this->applyStatus($shop, $token, $orderId, $label, $statusName);

            // --- Shipping
            case 'processing':
                return $this->shipEverything(
                    $shop, $token, $orderId,
                    $ctx['tracking'] ?? null,
                    (bool)($ctx['notify_customer'] ?? false)
                );

            case 'intransit':
                return $this->pushEvent($shop, $token, $orderId, 'in_transit', $ctx);

            case 'delivered':
                return $this->markDeliveredAndMaybeClose($shop, $token, $orderId, $ctx);

            // --- Return
            case 'return':
                //just make delevry faild
                return $this->pushEvent($shop, $token, $orderId, 'failure', $ctx);
                // return $this->openReturn($shop, $token, $orderId, $ctx);

            // --- Cancel
            case 'canceled':
                return $this->cancelOrder($shop, $token, $orderId, $ctx);

            default:
                return ['response'=>'error','message'=>'Unsupported status','status'=>400,'details'=>['normalized'=>$statusName]];
        }
    }



    /**
     * Apply a COD status to an order.
     * For CONFIRMED: add tag + upsert cod.status metafield.
     */
    public function applyStatus(string $shop, string $token, int $orderId, string $tagLabel, string $metaValue): array
    {
        // 1) Fetch order to merge tags
        $orderRes = Http::withHeaders(['X-Shopify-Access-Token'=>$token])
            ->get("https://{$shop}/admin/api/{$this->apiVersion}/orders/{$orderId}.json");
        if ($orderRes->failed()) {
            return ['response'=>'error','message'=>'Failed to fetch order','status'=>$orderRes->status(),'details'=>$orderRes->json()];
        }

        $order = $orderRes->json('order');
        $existing = collect(explode(',', (string)($order['tags'] ?? '')))
            ->map(fn($t)=>trim($t))->filter()->unique()->values();

        if (!$existing->contains($tagLabel)) {
            $existing->push($tagLabel);
        }

        // 2) Update tags
        $put = Http::withHeaders([
            'X-Shopify-Access-Token'=>$token,
            'Content-Type'=>'application/json',
        ])->put("https://{$shop}/admin/api/{$this->apiVersion}/orders/{$orderId}.json", [
            'order' => ['id'=>$orderId, 'tags'=>$existing->implode(', ')]
        ]);

        if ($put->failed()) {
            return ['response'=>'error','message'=>'Failed to update order tags','status'=>$put->status(),'details'=>$put->json()];
        }

        // 3) Upsert metafield cod.status = $metaValue
        $list = Http::withHeaders(['X-Shopify-Access-Token'=>$token])
            ->get("https://{$shop}/admin/api/{$this->apiVersion}/orders/{$orderId}/metafields.json");

        if ($list->failed()) {
            return ['response'=>'error','message'=>'Tags updated but metafield list failed','status'=>$list->status(),'details'=>$list->json()];
        }

        $existingMf = collect($list->json('metafields') ?? [])
            ->first(fn($mf)=>($mf['namespace']??'')==='cod' && ($mf['key']??'')==='status');

        if ($existingMf) {
            $mfId = $existingMf['id'];
            $upd  = Http::withHeaders([
                'X-Shopify-Access-Token'=>$token,
                'Content-Type'=>'application/json',
            ])->put("https://{$shop}/admin/api/{$this->apiVersion}/metafields/{$mfId}.json", [
                'metafield' => ['id'=>$mfId, 'type'=>'single_line_text_field', 'value'=>$metaValue]
            ]);
            if ($upd->failed()) {
                return ['response'=>'error','message'=>'Metafield update failed','status'=>$upd->status(),'details'=>$upd->json()];
            }
        } else {
            $crt = Http::withHeaders([
                'X-Shopify-Access-Token'=>$token,
                'Content-Type'=>'application/json',
            ])->post("https://{$shop}/admin/api/{$this->apiVersion}/metafields.json", [
                'metafield' => [
                    'namespace'=>'cod','key'=>'status','type'=>'single_line_text_field',
                    'value'=>$metaValue,'owner_id'=>$orderId,'owner_resource'=>'order'
                ]
            ]);
            if ($crt->failed()) {
                return ['response'=>'error','message'=>'Metafield create failed','status'=>$crt->status(),'details'=>$crt->json()];
            }
        }

        return [
            'response'=>'success',
            'result'=>[
                'order_id'=>$orderId,
                'tag'=>$tagLabel,
                'metafield'=>"cod.status={$metaValue}",
            ]
        ];
    }


    /**
     * === SHIPPED ===
     * Create a fulfillment for a fulfillment order (drives fulfillment_status).
     *
     */
    public function shipEverything(string $shop, string $token, int $orderId, ?array $tracking = null, bool $notify = false): array
    {
        // 1) Fetch Fulfillment Orders
        $foRes = Http::withHeaders(['X-Shopify-Access-Token' => $token])
            ->get("https://{$shop}/admin/api/{$this->apiVersion}/orders/{$orderId}/fulfillment_orders.json");

        if ($foRes->failed()) {
            return [
                'response' => 'error',
                'message'  => 'Failed to fetch fulfillment orders',
                'status'   => $foRes->status(),
                'details'  => $foRes->json(),
            ];
        }

        $fos = collect($foRes->json('fulfillment_orders') ?? [])
            ->filter(fn ($fo) => !in_array(($fo['status'] ?? ''), ['closed','cancelled','cancelled_and_restocked'], true))
            ->values();

        if ($fos->isEmpty()) {
            return ['response' => 'error', 'message' => 'No open fulfillment orders found for this order', 'status' => 404];
        }

        // 2) Build line_items_by_fulfillment_order (ship ALL remaining)
        $groups = [];
        foreach ($fos as $fo) {
            $lines = [];
            foreach (($fo['line_items'] ?? []) as $li) {
                $qty = (int)($li['remaining_quantity'] ?? 0);
                if ($qty <= 0) $qty = (int)($li['fulfillable_quantity'] ?? 0);
                if ($qty <= 0) $qty = (int)($li['quantity'] ?? 0);
                if ($qty > 0) {
                    $lines[] = ['id' => (int)$li['id'], 'quantity' => $qty];
                }
            }
            if (!empty($lines)) {
                $groups[] = [
                    'fulfillment_order_id' => (int)$fo['id'],
                    'fulfillment_order_line_items' => $lines,
                ];
            }
        }

        if (empty($groups)) {
            return ['response' => 'error', 'message' => 'Nothing left to fulfill for this order', 'status' => 422];
        }

        // 3) Create the fulfillment
        $payload = [
            'fulfillment' => [
                'line_items_by_fulfillment_order' => $groups,
                'notify_customer' => $notify,
            ],
        ];
        if ($tracking && is_array($tracking)) {
            // expects keys: number, company, url
            $payload['fulfillment']['tracking_info'] = array_intersect_key($tracking, array_flip(['number','company','url']));
        }

        $post = Http::withHeaders([
            'X-Shopify-Access-Token' => $token,
            'Content-Type'           => 'application/json',
        ])->post("https://{$shop}/admin/api/{$this->apiVersion}/fulfillments.json", $payload);

        if ($post->failed()) {
            return [
                'response' => 'error',
                'message'  => 'Fulfillment create failed',
                'status'   => $post->status(),
                'details'  => $post->json(),
                'sent'     => $payload, // helpful for debugging
            ];
        }

        return ['response' => 'success', 'result' => $post->json()];
    }


    /**
     * === IN_TRANSIT / OUT_FOR_DELIVERY / FAILURE / DELIVERED ===
     * Push a fulfillment event to update the order status page timeline.
     */
    private function pushEvent(string $shop, string $token, int $orderId, string $status, array $ctx = []): array
    {
        // status one of: in_transit, out_for_delivery, delivered, failure, ready_for_pickup, picked_up, attempted_delivery, canceled, label_printed, label_purchased
        $fulfillmentId = $ctx['fulfillment_id'] ?? null;
        if (!$fulfillmentId) {
            $fulfillmentId = $this->latestFulfillmentId($shop, $token, $orderId);
            if (!$fulfillmentId) {
                return ['response'=>'error','message'=>'No fulfillment found to attach event','status'=>404];
            }
        }

        $payload = ['event' => ['status' => $status]];
        if (!empty($ctx['message'])) {
            $payload['event']['message'] = (string) $ctx['message'];
        }

        $res = Http::withHeaders([
            'X-Shopify-Access-Token' => $token,
            'Content-Type' => 'application/json',
        ])->post("https://{$shop}/admin/api/{$this->apiVersion}/orders/{$orderId}/fulfillments/{$fulfillmentId}/events.json", $payload);

        if ($res->failed()) {
            return ['response'=>'error','message'=>'Push fulfillment event failed','status'=>$res->status(),'details'=>$res->json()];
        }

        return ['response'=>'success','result'=>$res->json()];
    }

    /**
     * === DELIVERED (+ optional close) ===
     */
    private function markDeliveredAndMaybeClose(string $shop, string $token, int $orderId, array $ctx = []): array
    {
        $event = $this->pushEvent($shop, $token, $orderId, 'delivered', $ctx);
        if (($event['response'] ?? 'error') !== 'success') {
            return $event;
        }

        if (!empty($ctx['close_after_delivered'])) {
            $close = Http::withHeaders(['X-Shopify-Access-Token'=>$token])
                ->post("https://{$shop}/admin/api/{$this->apiVersion}/orders/{$orderId}/close.json");
            if ($close->failed()) {
                return ['response'=>'error','message'=>'Order delivered but close failed','status'=>$close->status(),'details'=>$close->json()];
            }
            return ['response'=>'success','result'=>[
                'event'=>$event['result'] ?? null,
                'closed'=>$close->json()
            ]];
        }

        return $event;
    }

    /**
     * === RETURN_REQUESTED ===
     *
     */
    private function openReturn(string $shop, string $token, int $orderId, array $ctx): array
    {
        $orderGid = $ctx['order_gid'] ?? $this->orderGid($orderId);
        $returnLineItems = $ctx['return_line_items'] ?? [];

        if (empty($returnLineItems)) {
            return ['response'=>'error','message'=>'return_line_items is required','status'=>422];
        }

        $mutation = <<<'GQL'
                mutation($input: ReturnCreateInput!) {
                returnCreate(input: $input) {
                    return { id status }
                    userErrors { field message }
                }
                }
                GQL;

        $payload = [
            'query' => $mutation,
            'variables' => [
                'input' => [
                    'orderId' => $orderGid,
                    'returnLineItems' => $returnLineItems,
                ],
            ],
        ];

        $res = Http::withHeaders([
            'X-Shopify-Access-Token' => $token,
            'Content-Type' => 'application/json',
        ])->post("https://{$shop}/admin/api/{$this->apiVersion}/graphql.json", $payload);

        if ($res->failed()) {
            return ['response'=>'error','message'=>'Return create failed','status'=>$res->status(),'details'=>$res->json()];
        }

        $json = $res->json();
        $errors = data_get($json, 'data.returnCreate.userErrors', []);
        if (!empty($errors)) {
            return ['response'=>'error','message'=>'Return create userErrors','status'=>422,'details'=>$errors];
        }

        return ['response'=>'success','result'=>data_get($json, 'data.returnCreate.return')];
    }

    /**
     * === CANCELLED ===
     * Cancel an order with optional parameters.
     */
    private function cancelOrder(string $shop, string $token, int $orderId, array $ctx = []): array
    {
        $payload = [];

        foreach (['email','restock','reason','note','amount','currency'] as $k) {
            if (array_key_exists($k, $ctx)) $payload[$k] = $ctx[$k];
        }

        $res = Http::withHeaders([
            'X-Shopify-Access-Token' => $token,
            'Content-Type' => 'application/json',
        ])->post("https://{$shop}/admin/api/{$this->apiVersion}/orders/{$orderId}/cancel.json", $payload ?: []);

        if ($res->failed()) {
            return ['response'=>'error','message'=>'Order cancel failed','status'=>$res->status(),'details'=>$res->json()];
        }

        return ['response'=>'success','result'=>$res->json()];
    }

    /**
     * ===== Internals =====
     */

    private function getOrder(string $shop, string $token, int $orderId): ?array
    {
        $res = Http::withHeaders([
            'X-Shopify-Access-Token' => $token,
        ])->get("https://{$shop}/admin/api/{$this->apiVersion}/orders/{$orderId}.json");

        if ($res->failed()) {
            return null;
        }
        return $res->json('order');
    }

    /**
     * Update order tags
     */
    private function putOrderTags(string $shop, string $token, int $orderId, string $tags): bool
    {
        $res = Http::withHeaders([
            'X-Shopify-Access-Token' => $token,
            'Content-Type'           => 'application/json',
        ])->put("https://{$shop}/admin/api/{$this->apiVersion}/orders/{$orderId}.json", [
            'order' => [
                'id'   => $orderId,
                'tags' => $tags,
            ],
        ]);

        return $res->ok();
    }

    /**
     * Helpers to resolve IDs when ctx is incomplete
     */
    private function firstFulfillmentOrderId(string $shop, string $token, int $orderId): ?int
    {
        $res = Http::withHeaders([
            'X-Shopify-Access-Token' => $token,
        ])->get("https://{$shop}/admin/api/{$this->apiVersion}/orders/{$orderId}/fulfillment_orders.json");

        if ($res->failed()) return null;

        $arr = $res->json('fulfillment_orders') ?? [];
        if (empty($arr)) return null;

        // pick first open FO
        $fo = collect($arr)->first(fn($f) => ($f['status'] ?? null) !== 'closed') ?? $arr[0];
        return (int) ($fo['id'] ?? 0) ?: null;
    }

    private function latestFulfillmentId(string $shop, string $token, int $orderId): ?int
    {
        $res = Http::withHeaders([
            'X-Shopify-Access-Token' => $token,
        ])->get("https://{$shop}/admin/api/{$this->apiVersion}/orders/{$orderId}/fulfillments.json");

        if ($res->failed()) return null;

        $fulfillments = $res->json('fulfillments') ?? [];
        if (empty($fulfillments)) return null;

        // take the most recently created fulfillment
        usort($fulfillments, fn($a,$b) => strtotime($b['created_at']) <=> strtotime($a['created_at']));
        return (int) ($fulfillments[0]['id'] ?? 0) ?: null;
    }

    private function orderGid(int $orderId): string
    {
        return "gid://shopify/Order/{$orderId}";
    }

    /**
     * Upsert order metafield:
     * POST /metafields.json with owner_resource=order + owner_id
     * If a metafield exists with the same namespace/key, Shopify will create another unless we filter.
     * We first try to find existing one and PUT it; otherwise POST a new one.
     */
    private function upsertOrderMetafield(
        string $shop,
        string $token,
        int $orderId,
        string $namespace,
        string $key,
        string $type,
        string $value
    ): bool {
        // 1) Try to list existing metafields for this order (filter by namespace & key client-side)
        $list = Http::withHeaders([
            'X-Shopify-Access-Token' => $token,
        ])->get("https://{$shop}/admin/api/{$this->apiVersion}/orders/{$orderId}/metafields.json");

        if ($list->failed()) {
            return false;
        }

        $existing = collect($list->json('metafields') ?? [])
            ->first(fn ($mf): bool => ($mf['namespace'] ?? null) === $namespace && ($mf['key'] ?? null) === $key);

        if ($existing) {
            // 2) Update existing metafield
            $mfId = $existing['id'];
            $put  = Http::withHeaders([
                'X-Shopify-Access-Token' => $token,
                'Content-Type'           => 'application/json',
            ])->put("https://{$shop}/admin/api/{$this->apiVersion}/metafields/{$mfId}.json", [
                'metafield' => [
                    'id'    => $mfId,
                    'type'  => $type,
                    'value' => $value,
                ],
            ]);
            return $put->ok();
        }

        // 3) Create new metafield owned by order
        $post = Http::withHeaders([
            'X-Shopify-Access-Token' => $token,
            'Content-Type'           => 'application/json',
        ])->post("https://{$shop}/admin/api/{$this->apiVersion}/metafields.json", [
            'metafield' => [
                'namespace'      => $namespace,
                'key'            => $key,
                'type'           => $type,
                'value'          => $value,
                'owner_id'       => $orderId,
                'owner_resource' => 'order',
            ],
        ]);

        return $post->ok();
    }

    /** Merge new tag into existing tags, de-duping and preserving order */
    private function mergeTag(?string $existingTagsCsv, string $newTag): string
    {
        $items = collect(explode(',', (string) $existingTagsCsv))
            ->map(fn ($t) => trim($t))
            ->filter()
            ->values();

        // Add new tag if not already present, preserving order
        if (!$items->contains($newTag)) {
            $items->push($newTag);
        }
        // Shopify expects comma-separated string
        return $items->implode(', ');
    }

    /** Error response */
    private function err(string $msg, int $code = 400, array $extra = []): array
    {
        return [
            'response' => 'error',
            'message'  => $msg,
            'status'   => $code,
            'details'  => $extra,
        ];
    }


    /**
     * Fetch fulfillment orders for an order.
     */
    private function getFulfillmentOrders(string $shop, string $token, int $orderId): array
    {
        $res = Http::withHeaders(['X-Shopify-Access-Token'=>$token])
            ->get("https://{$shop}/admin/api/{$this->apiVersion}/orders/{$orderId}/fulfillment_orders.json");

        if ($res->failed()) return [];
        return $res->json('fulfillment_orders') ?? [];
    }

    /**
     * From provided items, group by fulfillment order.
     * Accepts either:
     *   - [{ id: FULFILLMENT_ORDER_LINE_ITEM_ID, quantity }]
     *   - or [{ line_item_id: ORDER_LINE_ITEM_ID, quantity }]  // auto-mapped to FO line item id
     */
    private function groupProvidedItemsByFO(array $items, array $fos, ?int $forceFoId = null): array
    {
        // Build maps: fo_id -> [fo_line_items], and id maps for quick lookups
        $foById = [];
        $foLineIdToFoId = [];        // FO line item id => FO id
        $orderLineIdToFoLineId = []; // order line item id => FO line item id

        foreach ($fos as $fo) {
            $foId = (int) ($fo['id'] ?? 0);
            if (!$foId) continue;

            // If a specific FO is requested, skip others
            if ($forceFoId && $foId !== (int)$forceFoId) continue;

            $foById[$foId] = $fo;
            foreach (($fo['line_items'] ?? []) as $li) {
                $foLiId    = (int) ($li['id'] ?? 0);
                $orderLiId = (int) ($li['line_item_id'] ?? 0);
                if ($foLiId)  $foLineIdToFoId[$foLiId] = $foId;
                if ($orderLiId && $foLiId) $orderLineIdToFoLineId[$orderLiId] = $foLiId;
            }
        }

        $grouped = []; // fo_id => [ ['id'=>fo_line_item_id,'quantity'=>q], ... ]

        foreach ($items as $it) {
            $qty = (int) ($it['quantity'] ?? 0);
            if ($qty <= 0) continue;

            // Accept either 'id' (assumed FO line item id) or 'line_item_id' (order line item id)
            $foLineId = null;
            if (isset($it['id'])) {
                $foLineId = (int) $it['id'];
            } elseif (isset($it['line_item_id'])) {
                $candidateOrderLi = (int) $it['line_item_id'];
                $foLineId = $orderLineIdToFoLineId[$candidateOrderLi] ?? null;
            }

            if (!$foLineId) continue;

            $foId = $foLineIdToFoId[$foLineId] ?? null;
            if (!$foId) continue;

            $grouped[$foId][] = ['id' => $foLineId, 'quantity' => $qty];
        }

        // Convert to Shopify shape
        $out = [];
        foreach ($grouped as $foId => $lineItems) {
            if (empty($lineItems)) continue;
            $out[] = [
                'fulfillment_order_id' => (int) $foId,
                'fulfillment_order_line_items' => array_values($lineItems),
            ];
        }

        return $out;
    }

    /**
     * Auto-fulfill ALL remaining quantities across all open fulfillment orders (or a specific one if forced).
     */
    private function autoBuildAllRemainingByFO(array $fos, ?int $forceFoId = null): array
    {
        $groups = [];

        foreach ($fos as $fo) {
            $foId = (int) ($fo['id'] ?? 0);
            if (!$foId) continue;

            // Skip closed/cancelled FO; keep open/in_progress/etc.
            $status = (string) ($fo['status'] ?? '');
            if (in_array($status, ['closed','cancelled','cancelled_and_restocked'], true)) {
                continue;
            }

            if ($forceFoId && $foId !== (int)$forceFoId) continue;

            $lines = [];
            foreach (($fo['line_items'] ?? []) as $li) {
                $foLineId = (int) ($li['id'] ?? 0);
                if (!$foLineId) continue;

                // Prefer a "remaining" field when present; fallback to fulfillable_quantity or quantity
                $qty =
                    (int) ($li['remaining_quantity'] ?? 0)
                    ?: (int) ($li['fulfillable_quantity'] ?? 0)
                    ?: (int) ($li['quantity'] ?? 0);

                if ($qty > 0) {
                    $lines[] = ['id' => $foLineId, 'quantity' => $qty];
                }
            }

            if (!empty($lines)) {
                $groups[] = [
                    'fulfillment_order_id' => $foId,
                    'fulfillment_order_line_items' => $lines,
                ];
            }
        }

        return $groups;
    }

}