<?php
namespace App\Services\Kpis;
use App\Services\Orders\FilterOrders;
use GlobalHelper;

class KpisConfirmation {

    protected FilterOrders $filterOrders;
    public function __construct() {
        $this->filterOrders = new FilterOrders();
    }

    /**
     * Get Liste Statuts
     */
    public function confirmationKpis($data) {
        // Retrieve the type of date for filtering or calculation
        $dateType = $this->data['dateType'] ?? null;

        // Fetch the base data required for calculations
        $data = $this->getBaseData($data);

        // Calculate the totals using the statuses and base data
        $totals = $this->calculateTotals($data);

        // Calculate the total orders and real orders based on date type and totals
        list($totalOrders, $totalReals) = $this->calculateTotalOrders($dateType, $totals, $data);

        // Calculate the rates based on totals, total orders, and total real orders
        $rates = $this->calculateRates($totals, $totalOrders, $totalReals);

        // Return the formatted final data containing all KPIs and their values
        return $this->formatFinalData($totals, $rates, $totalOrders, $totalReals);
    }
    
    /**
     * Returns the base data used for filtering orders.
     */
    private function getBaseData($data) {
        return array_merge($data,[
            'excludedOrderTypes' => ['followup', 'firstmile'],
            'count' => 1
        ]);
    }
    
    
    /**
     * Calculates the total number of orders for each status.
     */
    private function calculateTotals($data) {
        // Confirmed Orders
        $totalConfirmed = (float) $this->filterOrders->getOrders(array_merge($data, ['confirmed' => "yes"]));

        // Upsell Orders
        $totalUpselling = (float) $this->filterOrders->getOrders(array_merge($data, ['upsell' => "yes"]));

        // Grouped Status Data for Specific Status List
        $dataGrouped = array_merge($data, [
            'groupStatus' => 1,
            'listStatus' => [
                'newlead',
                'schedule',
                'wrongphonenumber',
                'test',
                'noanswer',
                'doubleorder',
                'canceled',
                'pending'
            ]
        ]);
        $totalGrouped = $this->filterOrders->getOrders($dataGrouped);

        // totalOther
        $totalOthers = ($totalGrouped['reported'] ?? 0) + // schedule
        ($totalGrouped['failed']            ?? 0) + // wrongphonenumber
        ($totalGrouped['test']              ?? 0) + // test
        ($totalGrouped['duplicate-order']   ?? 0) + // doubleorder
        ($totalGrouped['pending']           ?? 0);  // pending

        // Return result
        return  [
            'totalConfirmed'     => $totalConfirmed,
            'totalUpselling'     => $totalUpselling,
            'totalNew'           => $totalGrouped['waiting-pick-up']   ?? 0, // newlead
            'totalSchedule'      => $totalGrouped['reported']          ?? 0, // schedule
            'totalWrongPhone'    => $totalGrouped['failed']            ?? 0, // wrongphonenumber
            'totalTest'          => $totalGrouped['test']              ?? 0, // test
            'totalNoAnswer'      => $totalGrouped['no-answer']         ?? 0, // noanswer
            'totalDuplicate'     => $totalGrouped['duplicate-order']   ?? 0, // doubleorder
            'totalCanceled'      => $totalGrouped['canceled']          ?? 0, // canceled
            'totalPending'       => $totalGrouped['pending']           ?? 0, // pending
            'totalOthers' => $totalOthers,
        ];
    }


    /**
     * Calculates total orders and real orders based on the given date type.
     */
    private function calculateTotalOrders($dateType, $totals, $data): array{
        if ($dateType === "statusDate") {
            // totalOrders = tout sauf totalConfirmed et totalUpselling
            $totalOrders = array_sum(array_diff_key($totals, [
                'totalNew' => true,
                'totalUpselling' => true,
                'totalOthers' => true,
            ]));

            // totalReals = totalOrders - statuts à exclure
            $totalReals = $totalOrders
                - ($totals['totalWrongPhone'] ?? 0)
                - ($totals['totalTest'] ?? 0)
                - ($totals['totalDuplicate'] ?? 0)
                - ($totals['totalNew'] ?? 0)
                - ($totals['totalPending'] ?? 0);

        } else {
            // totalOrders = tout sauf totalUpselling
            $totalOrders = array_sum(array_diff_key($totals, [
                'totalUpselling' => true,
                'totalOthers' => true,
            ]));

            // totalReals = totalOrders - statuts à exclure
            $totalReals = $totalOrders
                - ($totals['totalWrongPhone'] ?? 0)
                - ($totals['totalTest'] ?? 0)
                - ($totals['totalDuplicate'] ?? 0)
                - ($totals['totalNew'] ?? 0)
                - ($totals['totalPending'] ?? 0);
        }

        // Return Result
        return [$totalOrders, $totalReals];
    }

    /**
     * Calculates the rates for different order statuses.
     */
    private function calculateRates($totals, $totalOrders, $totalReals) {
        return [
            'reals' => GlobalHelper::calculRate(['total' => $totalReals, 'baseTotal' => $totalOrders]),
            'confirmed' => GlobalHelper::calculRate(['total' => $totals['totalConfirmed'], 'baseTotal' => ($totalReals - $totals['totalSchedule'])]),
            'confirmedReel' => GlobalHelper::calculRate(['total' => $totals['totalConfirmed'], 'baseTotal' => $totalOrders]),
            'noAnswer' => GlobalHelper::calculRate(['total' => $totals['totalNoAnswer'], 'baseTotal' => $totalReals]),
            'canceled' => GlobalHelper::calculRate(['total' => $totals['totalCanceled'], 'baseTotal' => $totalReals]),
            'new' => GlobalHelper::calculRate(['total' => $totals['totalNew'], 'baseTotal' => $totalOrders]),
            'upselling' => GlobalHelper::calculRate(['total' => $totals['totalUpselling'], 'baseTotal' => $totalReals]),
            'pending' => GlobalHelper::calculRate(['total' => $totals['totalPending'], 'baseTotal' => $totalReals]),
            'schedule' => GlobalHelper::calculRate(['total' => $totals['totalSchedule'], 'baseTotal' => $totalReals]),
            'wrongPhone' => GlobalHelper::calculRate(['total' => $totals['totalWrongPhone'], 'baseTotal' => $totalReals]),
            'test' => GlobalHelper::calculRate(['total' => $totals['totalTest'], 'baseTotal' => $totalReals]),
            'duplicate' => GlobalHelper::calculRate(['total' => $totals['totalDuplicate'], 'baseTotal' => $totalReals]),
            'others' => GlobalHelper::calculRate(['total' => $totals['totalOthers'], 'baseTotal' => $totalReals]),
        ];
    }
    
    /**
     * Formats the final output data containing totals and calculated rates.
     */
    private function formatFinalData($totals, $rates, $totalOrders, $totalReals) {
        $finalData = array_reduce(array_keys($totals), function ($carry, $key) use ($totals, $rates) {
            $carry["total" . ucfirst(str_replace('total','',$key))] = $totals[$key];
            $carry["rate" . ucfirst(str_replace('total','',$key))] = $rates[lcfirst(str_replace('total','',$key))] ?? null;
            return $carry;
        }, []);
    
        return array_merge([
            'totalLeads' => $totalOrders,
            'rateLeads' => 100,
            'totalReals' => $totalReals,
            'rateReals' => $rates['reals'],
            'rateConfirmedReel' => $rates['confirmedReel'] // Ensure it's explicitly included
        ], $finalData);
    }
}