<?php

namespace App\Http\Controllers\Auth;

use App\Http\Controllers\Controller;
use App\Models\Countries;
use App\Models\SellersLivraison;
use App\Services\Fields\FieldsService;
use App\Services\Sellers\FilterSellers;
use App\Services\Sellers\SellersService;
use GlobalHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use SellersHelper;

class SellerController extends Controller{
    protected FilterSellers $FilterSellers;
    protected FieldsService $FieldsService;
    protected SellersService $SellersService;

    /**
     * Construct
     */
    public function __construct(){
        $this->FilterSellers = new FilterSellers();
        $this->FieldsService = new FieldsService();
        $this->SellersService = new SellersService();
    }

    /**
     * Get fees for the specified 'from' and 'to' locations.
     *
     * @param Request $request The HTTP request containing 'from' and 'to' parameters
     * @return JsonResponse A JSON response with fees or validation errors
     */
    public function fees(Request $request){
        // Current Fees
        $seller = SellersHelper::CurrentSeller();

        // Validate Params
        $validateRequest = $this->validateFees($request);
        if (($validateRequest['response'] ?? null) === 'error') { return response()->json($validateRequest, 400); }

        // Load Fees
        $resultFees = $this->loadFees($request,$validateRequest);

        // Return Response
        return response()->json([
            'response' => 'success',
            'result' => $resultFees,
        ]);
    }

    /**
     * validate Params of fees
     */
    private function validateFees(Request $request){
        // Étape 1 : Valider que les champs sont requis et entiers
        $validator = Validator::make($request->all(), [
            'originCountry' => 'required|integer',
            'destinationCountry' => 'required|integer',
        ]);

        if ($validator->fails()) {
            return [
                'response' => 'error',
                'message' => implode(", ", $validator->messages()->all()),
            ];
        }

        // Étape 2 : Vérifier que les pays existent
        $origin = Countries::find($request->originCountry);
        $destination = Countries::find($request->destinationCountry);

        if (!$origin || !$destination) {
            return [
                'response' => 'error',
                'message' => 'Origin or destination country not found.',
            ];
        }

        // Si tout est bon, retourner succès
        return [
            'response' => 'success',
            'originCountry' => $origin,
            'destinationCountry' => $destination,
        ];
    }



    /**
     * load Fees
     */
    private function loadFees($request,$validateRequest){
        $originCountry = $request->originCountry;
        $destinationCountry = $request->destinationCountry;
        $grossWeight = $request->grossWeight;
        $length = $request->length;
        $width = $request->width;
        $height = $request->height;

        // Load Countries
        $rowOriginCountry = $validateRequest['originCountry'];
        $rowDestinationCountry = $validateRequest['destinationCountry'];

        $seller = SellersHelper::CurrentSeller();
        $customFees = SellersLivraison::where('seller_id', $seller->id)->get();

        // Cod Fees
        $codData = $this->getCodFees($customFees, $originCountry, $destinationCountry, $seller);

        // Shipping Fees
        $shippingFees = $this->getShippingFees($seller,$customFees, $originCountry, $destinationCountry, $grossWeight, $length, $width, $height);

        // Call Center Fees
        $callcenterFees = $this->getCallCenterFees($customFees, $originCountry, $destinationCountry, $seller);

        // Return Result
        return [
            'originCountry' => $rowOriginCountry->name,
            'destinationCountry' => $rowDestinationCountry->name,
            'confirmationFees' => $callcenterFees['confirmationFees'],
            'enteredLead' => $callcenterFees['enteredLead'],
            'deliveredFees' => $callcenterFees['deliveredFees'],
            'shippingType' => $shippingFees['shippingType'],
            'shippingFees' => $shippingFees['shippingFees'],
            'returnFees' =>  $shippingFees['returnFees'],
            'codFeesType' => $codData['type'] === "flaterate" ? "flaterate" : "percentage",
            'codFees' => $codData['value'],
            'fulfillmentFees' => $this->getFulfillmentFees($customFees, $originCountry, $destinationCountry, $seller),
            'vWeight' => $this->calculateVolumetricWeight($length, $width, $height),
        ];
    }


    /**
     * Calcule les frais de fulfillment (préparation de commande)
     */
    private function getFulfillmentFees($customFees, $origin, $destination, $seller){
        foreach ($customFees as $row) {
            if ($row->from_country_id == $origin && $row->country_id == $destination && !is_null($row->fulfillment)) {
                return floatval($row->fulfillment);
            }
        }
        return floatval($seller->fulfillment);
    }

    /**
     * Calculate Call Center Fees
     */
    private function getCallCenterFees($customFees, $origin, $destination, $seller){
        // Initial Result
        $result =  [
            'confirmationFees' => floatval($seller->price_callcenter),
            'enteredLead' => floatval($seller->price_order),
            'deliveredFees' => floatval($seller->price_callcenter_delivered),
        ];

        // Loop List of fees
        foreach ($customFees as $row) {
            if ($row->from_country_id == $origin && $row->country_id == $destination) {
                if(!is_null($row->price_callcenter)){ $result['confirmationFees'] = floatval($row->price_callcenter);  }
                if(!is_null($row->price_order)){ $result['enteredLead'] = floatval($row->price_order);  }
                if(!is_null($row->price_callcenter_delivered)){ $result['deliveredFees'] = floatval($row->price_callcenter_delivered);  }
            }
        }

        // Return Result
        return $result;
    }

    /**
     * Calcule les frais de contre-remboursement (COD) et son type
     */
    private function getCodFees($customFees, $origin, $destination, $seller){
        foreach ($customFees as $row) {
            if ($row->from_country_id == $origin && $row->country_id == $destination && (!is_null($row->cod_pourc_value) || !is_null($row->cod_flaterate))) {
                return [
                    'value' =>  $row->cod_type_calcul == "flaterate" ? floatval($row->cod_flaterate) : floatval($row->cod_pourc_value),
                    'type' => $row->cod_type_calcul ?? "percentage"
                ];
            }
        }
        return [
            'value' => floatval($seller->cod_pourc_value),
            'type' => $seller->cod_type_calcul ?? "percentage"
        ];
    }
    /**
     * Calcule les frais d'expédition selon le poids, les dimensions et les règles personnalisées
     */
    private function getShippingFees($seller,$customFees, $origin, $destination, $grossWeight, $length, $width, $height){
        $shippingFees = 0;
        $returnFees = $seller->price_return;
        $shippingType = $seller->base_shipping ?? "shipped";

        foreach ($customFees as $row) {
            if ($row->from_country_id == $origin && $row->country_id == $destination) {
                $lastmileType = $row->lastmile_type ?? "weight";
                $shippingFees = $lastmileType === "weight" ? $row->lastmile_pricecod : $row->lastmile_fixedcod;
                $shippingFees = floatval($shippingFees ?? 0);

                // Shipping Type
                $shippingType = $seller->base_shipping ?? "shipped";

                // Check return Fees
                if(!is_null($row->return_price)){
                    $returnFees = $row->return_price;
                }

                if (!is_null($grossWeight)) {
                    $vWeight = $this->calculateVolumetricWeight($length, $width, $height);
                    $compareWeight = ($origin != $destination) || ($grossWeight <= 5);
                    $finalWeight = $compareWeight ? max($grossWeight, $vWeight) : $grossWeight;

                    $firstWeight = $row->lastmile_firstweight ?? 0;
                    $additionalWeight = $finalWeight - $firstWeight;
                    $addPerWeight = $row->lastmile_additionalweight ?? 0;
                    $addPrice = $row->lastmile_additionalprice ?? 0;

                    if ($additionalWeight > 0 && $addPerWeight > 0) {
                        $devisWeight = GlobalHelper::ConvertCeiling($additionalWeight / $addPerWeight);
                        $additional = $devisWeight * $addPrice;
                        $shippingFees = $shippingFees + $additional;
                    }
                }
            }
        }

        return [
            "shippingFees" => $shippingFees,
            "returnFees" => $returnFees,
            "shippingType" => $shippingType,
        ];
    }

    /**
     * Calcule le poids volumétrique à partir des dimensions
     */
    private function calculateVolumetricWeight($length, $width, $height){
        return round(($length * $width * $height) / 5000, 2);
    }



}