<?php
namespace App\Services\Warehousing;

use App\Models\SellersStockOperations;
use App\Services\Orders\OrderFlowService;
use App\Services\Shipping\FilterWarehouses;
use App\Models\SellersStock;
use App\Models\Warehouses;
use App\Services\Shipping\FilterCountries;
use Illuminate\Support\Facades\DB;

class InventoryService{
    protected FilterWarehouses $FilterWarehouses;
    protected FilterProducts $FilterProducts;
    protected OrderFlowService $OrderFlowService;
    protected FilterCountries $FilterCountries;

    /**
     * Inject the FilterProducts service into the controller
     */
    public function __construct(){
        $this->FilterWarehouses = new FilterWarehouses();
        $this->FilterProducts = new FilterProducts();
        $this->OrderFlowService = new OrderFlowService();
        $this->FilterCountries = new FilterCountries();
    }


    /**
     * Prépare les produits, entrepôts et pays associés
     */
    private function prepareProductContext($rowProduct){
        // Liste des produits : soit les variantes, soit le produit lui-même
        $listeProducts = ($rowProduct->productType === "variable")
            ? $this->FilterProducts->getProducts([
                'parent' => $rowProduct->id,
                'noPaginate' => 1,
            ])
            : [$rowProduct];

        $listeProductsIds = array_map(fn($product) => $product->id, $listeProducts);

        $warehouseIds = SellersStock::whereIn('parent', $listeProductsIds)->pluck('warehouse_id')->all();
        $listeWarehouses = Warehouses::whereIn('id', $warehouseIds)->get();

        $countryIds = $listeWarehouses->pluck('country_id')->all();
        $listCountries = $this->FilterCountries->getCountries([
            'listeIds' => $countryIds,
            'noPaginate' => 1,
            'noFormatter' => 1,
        ]);

        return [$listeProducts, $listeWarehouses, $listCountries];
    }

    /*
        calculate Inventory Status
    */
    public function calculateInventoryStatus($rowProduct, $options = []){
        // Initial params
        $loadDetails = $options['loadDetails'] ?? null;
        $inventoryResults = [];
        $productInventory = [];

        // Check Product
        if ($rowProduct->id ?? null) {
            $productType = $rowProduct->productType;

            if ($rowProduct->parent == 0 || is_null($rowProduct->parent) || $productType == "variant") {

                // Is Variable
                if ($productType == "variable") {
                    $listeVariantes = $this->FilterProducts->getProducts([
                        'parent' => $rowProduct->id,
                        'noPaginate' => 1,
                        'noFormatter' => 1,
                    ]);

                    if (count($listeVariantes ?? [])) {
                        foreach ($listeVariantes as $rowVariant) {
                            $stock_variante = $this->calculateInventoryStatus($rowVariant, ['loadDetails' => 1]);
                            $inventoryResults[] = $stock_variante;
                        }
                    }
                } else {
                    $listeStocks = $this->FilterProducts->getProducts([
                        'parent' => $rowProduct->id,
                        'noPaginate' => 1,
                        'noFormatter' => 1,
                    ]);
                    $inprogessQty = 0;
                    $delivredQty = 0;
                    $leftQty = 0;
                    if (count($listeStocks ?? []) > 0) {
                        foreach ($listeStocks as $row_stock) {
                            $info_products = $this->calculateInventoryStatus($row_stock);

                            $inprogessQty = $inprogessQty + $info_products['inprogessQty'];
                            $delivredQty = $delivredQty + $info_products['delivredQty'];
                            $leftQty = $leftQty + $info_products['leftQty'];
                            $inventoryResults[] = $info_products;
                        }
                    }

                    $info = [
                        'inprogessQty' => $inprogessQty,
                        'delivredQty' => $delivredQty,
                        'leftQty' => $leftQty,
                    ];
                }
            } else {
                $total_manual_supply = 0;
                if ($rowProduct->shippingType == "firstmile") {
                    $delivredQty = $this->deliveredOrders($rowProduct, 1);
                    $total_manual_supply = $this->fetchStockOperations([
                        'productId' => $rowProduct->id,
                        'sumQty' => 1,
                        'type' => 'supply',
                    ]);

                    $totalQty = $delivredQty + $total_manual_supply;
                    $inprogessQty = $rowProduct->quantity;
                    $leftQty = $totalQty - $inprogessQty;
                } else {
                    $totalQty = $rowProduct->quantity;
                    $inprogessQty =  $this->InProgressOrders($rowProduct);
                    $delivredQty = $this->deliveredOrders($rowProduct, 1);
                    $returnQty = $this->ReturnOrders($rowProduct, 1);
                    $leftQty = $totalQty - $inprogessQty - $delivredQty - $returnQty;
                }

                $productInventory= [
                    'stockId' => $rowProduct->id,
                    'stockName' => $rowProduct->name,
                    'productId' => $rowProduct->parent,
                    'warehouseId' => $rowProduct->warehouseId,
                    'totalQty' => $totalQty ?: 0,
                    'inprogessQty' => $inprogessQty ?: 0,
                    'delivredQty' => $delivredQty ?: 0,
                    'leftQty' => $leftQty ?: 0,
                ];
            }


            if ($loadDetails == 1) {
                return [
                    'inventoryResults' => $inventoryResults,
                    'productType' => $productType,
                ];
            }

            return $productInventory;
        }
    }

    /*
        Handle inventory for a variable product (parent product with variants)
    */
    private function handleVariableProduct($rowProduct): array{
        $results = [];
        $variants = $this->FilterProducts->getProducts([
            'parent' => $rowProduct->id,
            'noPaginate' => 1,
            'noFormatter' => 1,
        ]);

        foreach ($variants ?? [] as $variant) {
            $results[] = $this->calculateInventoryStatus($variant, ['loadDetails' => 1]);
        }

        return $results;
    }

    /*
        Handle inventory aggregation for a variant group (children of a non-variable parent)
    */
    private function handleSimpleProduct($rowProduct, $loadDetails){
        $inventoryResults = [];
        $inprogessQty = $delivredQty = $leftQty = 0;

        $stocks = $this->FilterProducts->getProducts([
            'parent' => $rowProduct->id,
            'noPaginate' => 1,
            'noFormatter' => 1,
        ]);

        foreach ($stocks ?? [] as $stock) {
            $info = $this->calculateInventoryStatus($stock);
            $inprogessQty += $info['inprogessQty'];
            $delivredQty  += $info['delivredQty'];
            $leftQty      += $info['leftQty'];
            $inventoryResults[] = $info;
        }

        if ($loadDetails == 1) {
            return [
                'inventoryResults' => $inventoryResults,
                'productType' => $rowProduct->productType,
            ];
        }

        return [
            'inprogessQty' => $inprogessQty,
            'delivredQty' => $delivredQty,
            'leftQty' => $leftQty,
        ];
    }

    /*
        Handle inventory for a single variant (individual stock row)
    */
    private function handleSingleVariant($rowProduct): array{
        if ($rowProduct->shippingType === "firstmile") {
            $delivredQty = $this->deliveredOrders($rowProduct, 1);
            $manualSupply = $this->fetchStockOperations([
                'productId' => $rowProduct->id,
                'sumQty' => 1,
                'type' => 'supply',
            ]);

            $totalQty = $delivredQty + $manualSupply;
            $inprogessQty = $rowProduct->quantity;
            $leftQty = $totalQty - $inprogessQty;
        } else {
            $totalQty = $rowProduct->quantity;
            $inprogessQty = $this->InProgressOrders($rowProduct);
            $delivredQty = $this->deliveredOrders($rowProduct, 1);
            $leftQty = $totalQty - $inprogessQty - $delivredQty;
        }

        return [
            'stockId' => $rowProduct->id,
            'stockName' => $rowProduct->name,
            'productId' => $rowProduct->parent,
            'warehouseId' => $rowProduct->warehouseId,
            'totalQty' => $totalQty ?: 0,
            'inprogessQty' => $inprogessQty ?: 0,
            'delivredQty' => $delivredQty ?: 0,
            'leftQty' => $leftQty ?: 0,
        ];
    }

    /*
        Get delivered Orders
    */
    private function DeliveredOrders($rowProduct,$options = []){
        if ($rowProduct->id ?? null) {
            $results = $this->OrderFlowService->getProducts([
                'productId' => $rowProduct->id,
                'status' => "delivered",
                'count' => 1,
            ]);

            return $results;
        }
    }

    /*
        Get Return Orders
    */
    private function ReturnOrders($rowProduct,$options = []){
        if ($rowProduct->id ?? null) {
            $results = $this->OrderFlowService->getProducts([
                'productId' => $rowProduct->id,
                'status' => "undelivered",
                'count' => 1,
            ]);

            return $results;
        }
    }

    /*
        In Progess Ordrers
    */
    private function InProgressOrders($rowProduct,$options = []){
        if ($rowProduct->id ?? null) {
            // Initial params
            $loadDetails = $options['loadDetails'] ?? null;

            $results = [];

            if ($loadDetails) {
                $total_in_transit = $this->OrderFlowService->getProducts([
                    'productId' => $rowProduct->id,
                    'status' => "in-transit",
                    'sumQty' => 1,
                ]);
                $total_shipped = $this->OrderFlowService->getProducts([
                    'product_id' => $rowProduct->id,
                    'status' => "shipped",
                    'sumQty' => 1,
                ]);
                $total_return = $this->OrderFlowService->getProducts([
                    'productId' => $rowProduct->id,
                    'status' => "undelivered",
                    'sumQty' => 1,
                ]);

                $results = [
                    'total_in_transit' => $total_in_transit,
                    'total_shipped' => $total_shipped,
                    'total_return' => $total_return,
                ];
            } else {
                $results = $this->OrderFlowService->getProducts([
                    'productId' => $rowProduct->id,
                    'status' => "inprogress",
                    'sumQty' => 1,
                ]);
            }

            return $results;
        }
    }

   /**
     * Get list of operations with optional filters and modes.
     */
    public function fetchStockOperations(array $data = []){
        $productId     = $data['productId']     ?? null;
        $type          = $data['type']           ?? null;
        $count         = $data['count']          ?? null;
        $limit         = $data['limit']          ?? null;
        $offset        = $data['offset']         ?? null;
        $sumQty = $data['sumQty'] ?? null;

        $query = SellersStockOperations::orderByDesc('id');

        if ($productId) {
            $query->where('product_id', $productId);
        }

        if ($type && $type !== 'all') {
            $query->where('operation_type', $type);
        }

        if ($sumQty == 1) {
            if (!$type) {
                $query->whereNotIn('operation_type', ['supply']);
            }
            return $query->sum('quantity');
        }

        if ($limit) { $query->limit($limit); }

        if ($offset) {
            $query->offset($offset);
        }

        if ($count == 1) {
            return $query->count();
        }

        return $query->get();
    }


    // new Stock Invoentory Process

    public function inventoryMovement($rowProduct, $filterCountryId = null)
    {
        // If Variant/Variable: fetch children and recurse
        if ($this->isVariableOrVariant($rowProduct)) {
            // just return a custom list of variants
            return $this->getVariants($rowProduct);

        }
        // Simple product (stock row): build inventory block
        return $this->buildSimpleProductInventory($rowProduct, $filterCountryId);
    }

    private function getVariants($rowProduct)
    {
        $children = SellersStock::where('parent', $rowProduct->id)->get();
        return $children->map(fn($child) => [
            'productId' => $child->id,
            'productName' => $child->name,
            'productSku' => $child->reference,
            'remainingQuantity' => $child->remaining_quantity,
        ] )->values()->toArray();
    }

    private function getAvailableCountries($rowProduct)
    {
        $warehouseIds =  SellersStock::where('parent', $rowProduct->id)->OrWhere('id', $rowProduct->id)->distinct('warehouse_id')->pluck('warehouse_id')->all();

        $warehouses = Warehouses::whereIn('id', $warehouseIds)->get();
        return $warehouses->pluck('country_id')->unique()->values()->toArray();
    }

    // helpers
    private function isVariableOrVariant($rowProduct): bool
    {
        // Make sure $rowProduct is an object (Eloquent) not array
        return in_array($rowProduct->productType, ['variable', 'variant'], true);
    }

    /**
     * Build the inventory block for a simple product; returns null if no matching warehouses/ops.
     */
    private function buildSimpleProductInventory($rowProduct, ?int $filterCountryId)
    {
        $stocks = SellersStock::where('parent', $rowProduct->id)->get();
        if ($stocks->isEmpty()) {
            $self = SellersStock::find($rowProduct->id);
            $stocks = $self ? collect([$self]) : collect();
        }

        $warehouses = $this->getWarehouseForProduct($stocks, $filterCountryId);
        if ($warehouses->isEmpty()) {
            return null;
        }

        $operations = $this->getOperationsForProduct($stocks);
        // Build a FLAT array of op objects (no nested arrays)
        $resultStocks = collect($stocks)
            ->flatMap(function ($stock) use ($warehouses, $operations) {
                $warehouse = $warehouses->firstWhere('id', $stock->warehouse_id);
                if (!$warehouse) {
                    return collect(); // skip this stock if not in the filtered warehouses
                }

                return $operations
                    ->where('product_id', $stock->id)
                    ->map(fn($op) => array_merge(
                        $this->mapOperationWithOrders($op),
                        [
                            'countryId'   => $warehouse->country_id,
                            'countryName' => $warehouse->country_name,
                            // you can also add 'warehouseId' => $warehouse->id if needed
                        ]
                    ));
            })
            ->values()
            ->toArray();

        if (empty($resultStocks)) {
            return null;
        }

        return [
            'productId'   => $rowProduct->id,
            'productName' => $rowProduct->name ?? null,
            'productSku'  => $rowProduct->sku ?? $rowProduct->reference ?? null,
            'stocks'      => $resultStocks, // <-- flat array of operation objects
        ];
    }


    /**
     * Get the warehouses for a set of stock rows (Collection or array), honoring country filter.
     */
    private function getWarehouseForProduct($stocks, ?int $filterCountryId)
    {
        // Accept both Collection and array
        $warehouseIds = collect($stocks)->pluck('warehouse_id')->filter()->unique()->values();
        if ($warehouseIds->isEmpty()) {
            return collect(); // return empty Collection
        }

        return Warehouses::whereIn('id', $warehouseIds)
            ->when($filterCountryId !== null, fn($q) => $q->where('country_id', $filterCountryId))
            ->get();
    }

    /**
     * Get ordered operations for a set of stock rows (Collection or array).
     */
    private function getOperationsForProduct($stocks = [])
    {
        $productIds = collect($stocks)->pluck('id')->filter()->unique()->values();
        if ($productIds->isEmpty()) {
            return collect();
        }

        return SellersStockOperations::whereIn('product_id', $productIds)
            ->orderBy('created_at', 'desc')
            ->get();
    }

    private function mapOperationWithOrders($op): array
    {
        $type = $op->operation_type ?? $op->type;

        $item = [
            'type'            => $type,
            'description'     => $op->operation_description ?? null,
            'comment'         => $op->comment ?? null,
            'quantity'        => $op->quantity,
            'currentQuantity' => $op->current_stock,
            'createdAt'       => $op->created_at,
        ];

        if ($type === 'collect') {
            $item['orders'] = $this->getCollectOrdersForOperation($op->id);
        }

        return $item;
    }


    /**
     * Fetch order rows tied to a 'collect' operation_id from operation_order_details.
     */
    private function getCollectOrdersForOperation(int $operationId): array
    {
        return DB::table('operation_order_details')
            ->where('operation_id', $operationId)
            ->get(['order_id', 'order_code', 'quantity'])
            ->map(fn($r) => [
                'order_id'   => $r->order_id,
                'order_code' => $r->order_code,
                'quantity'   => $r->quantity,
            ])
            ->toArray();
    }

    // return list of country IDs for all stocks of this product
    // (and, if it's variable/variant, for all its variant children)
    public function getStockCountries($rowProduct)
    {
        // Variant/Variable → aggregate countries from all children
        if ($this->isVariableOrVariant($rowProduct)) {
            $children = SellersStock::where('parent', $rowProduct->id)->get();

            // getAvailableCountries($child) returns an array; collect + flatten + unique
            return collect($children)
                ->map(fn($child) => $this->getAvailableCountries($child)) // array per child
                ->flatten()                                               // merge arrays
                ->unique()                                                // dedupe
                ->values()
                ->toArray();
        }

        // Simple product (or a stock row) → just use the helper directly
        return $this->getAvailableCountries($rowProduct);
    }



}