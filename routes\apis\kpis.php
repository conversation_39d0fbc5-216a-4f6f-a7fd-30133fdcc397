<?php

use App\Http\Controllers\Kpis\FundsController;
use App\Http\Controllers\Kpis\KpisController;
use App\Http\Controllers\Kpis\OrdersController;
use Illuminate\Support\Facades\Route;

// Manage kpis Routes
Route::prefix('kpis')->name('kpis.')->group(function () {
    Route::get('confirmation', [KpisController::class, 'confirmation'])->name('confirmation'); // Confirmation kpis
    Route::get('shipping', [KpisController::class, 'shipping'])->name('shipping'); // Shipping kpis
    Route::get('followup', [KpisController::class, 'followup'])->name('followup'); // Followup kpis
    Route::get('delevredRate/{year}', [KpisController::class, 'delevredRate'])->name('delevredRate'); // Followup kpis


    Route::get('orders', [OrdersController::class, 'orders'])->name('orders'); // Orders kpis
    Route::get('funds', [FundsController::class, 'funds'])->name('funds'); // Funds kpis
    Route::get('profits/{year}', [FundsController::class, 'profits'])->name('profits'); // Followup kpis

});