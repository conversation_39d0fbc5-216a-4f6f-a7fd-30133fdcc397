<?php
use App\Http\Controllers\LeadSources\YoucanImportsController;
use Illuminate\Support\Facades\Route;


Route::prefix('youcan')->name('youcan.')->group(function () {

    Route::get('/auth/install', [YoucanImportsController::class, 'install'])->middleware('auth.token');
    Route::get('/auth/callback', [YoucanImportsController::class, 'callback']);
    Route::post('/addSourceLead', [YoucanImportsController::class, 'addLightfSourceLead'])->middleware('auth.token');

    //must be public and open
    Route::post('/auth/webhook/{id}', [YoucanImportsController::class, 'handleOrderWebhook'])->name('webhook');
});