<?php

namespace App\Http\Controllers\Orders;

use App\Http\Controllers\Controller;
use App\Services\Orders\FilterOrders;
use App\Services\Orders\OrderStatusService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class FollowupController extends Controller{
    protected OrderStatusService $OrderStatusService;
    protected FilterOrders $filterOrders;

    /**
     * Inject the FilterOrders service into the controller
     */
    public function __construct(){
        $this->OrderStatusService = new OrderStatusService();
        $this->filterOrders = new FilterOrders();
    }

    /**
     * List of status
     */
    public function statuses(){
        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $this->OrderStatusService->followupStatusList([
                "getList" => 1,
            ]),
            'followupStatusesInMenu' => ['new','delivery','rtc','no-answer'],
        ]);
    }

     /**
     * Count of orders
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function countOrders(Request $request): JsonResponse{
        // Fetch Grouped Count Orders
        $groupedCounts = $this->groupedCountOrders($request);

        // Mapping Grouped Count
        $result = $this->mappingCountOrders($groupedCounts);

        // Retourner la réponse JSON
        return response()->json([
            'response' => 'success',
            'result' => $result
        ]);
    }

    /**
     * groupedCountOrders
     */
    private function groupedCountOrders($request){
        // Préparer les filtres avec le groupement par statut
        $filters = array_merge(['groupStatus' => 1], $request->all());

         // Fetch the list of followup statuses
        $listeStatus = $this->getFollowupStatuses();

        // Grouped Status
        $filters = array_merge($request->all(), ['groupStatus' => 1,'groupedField' => 'feedbacktype','listeFollowupStatus' => $listeStatus]);
        $groupedCounts = $this->filterOrders->getOrders($filters);

        // New Followup
        $groupedCounts['new'] = (float) $this->filterOrders->getOrders(array_merge($request->all(), ['listeFollowupStatus' => ["new"]]));
        
        // Return Result
        return $groupedCounts;
    }
    
    /**
     * mappingCountOrders
     */
    private function mappingCountOrders($groupedCounts){
        // Fetch the list of followup statuses
        $listeStatus = $this->getFollowupStatuses();

        // Initial Result
        $result = [];
        $result["total"] = 0;

        // Iterate over the statuses and calculate the total and rate for each
        foreach ($listeStatus as $keyStatus) {
             // Get Total By Status
            $totalStatus = (int) ($groupedCounts[$keyStatus] ?? 0 );

            // Calcul total
            $result["total"] += $totalStatus;

            // Update Mapping
            $result[$keyStatus] = $totalStatus;
        }

        // Return Result
        return $result;
    }

    /**
     * Returns the list of followup statuses.
     */
    private function getFollowupStatuses() {
        $listeStatus = $this->OrderStatusService->getFollowupStatuses(['arKeys' => 1]);
        array_unshift($listeStatus, "new"); // Add "new" status at the beginning
        return $listeStatus;
    }
}