<?php

namespace App\Http\Controllers\Sourcing;

use App\Http\Controllers\Controller;
use App\Models\SourcingInvoices;

;
use App\Services\Invoices\InvoicesService;
use App\Services\Sourcing\FilterSourcingInvoices;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use SellersHelper;

class SourcingInvoicesController extends Controller{
    protected FilterSourcingInvoices $FilterSourcingInvoices;
    protected InvoicesService $InvoicesService;
    protected $currentSeller;
    /**
     * Construct
     */
    public function __construct(){
        $this->FilterSourcingInvoices = new FilterSourcingInvoices();
        $this->InvoicesService = new InvoicesService();
        $this->currentSeller = SellersHelper::CurrentSeller();
    }

    /**
     * Retrieve and return a list of filtered invoices.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse{
        // Retrieve the list of invoices based on the provided filters
        $result = $this->FilterSourcingInvoices->getInvoices($request->all());

        $results = [
            'response' => 'success',
            'result' => $request->count ? $result : ($result['items'] ?? []),
        ];

        // Include pagination details only if count is not requested
        if (!$request->count) {
            $results['paginate'] = [
                'count' => $result['count'] ?? 0,
                'currentPage' => $result['currentPage'] ?? 1,
                'totalPages' => $result['totalPages'] ?? 1,
            ];
        }

        // Return the response as a JSON object
        return response()->json($results);
    }

    public function details(Request $request, $id): JsonResponse
    {
        // Retrieve the invoice details by ID
        $invoice = SourcingInvoices::where('id', $id)
            ->first();

        if (!$invoice) {
            // Return a not found response if the invoice does not exist
            return response()->json([
                'response' => 'error',
                'message' => 'Invoice not found',
            ], 404);
        }

        // Format the invoice using the sourcingInvoices.json fields
        $formattedInvoice = $this->FilterSourcingInvoices->formatInvoice($invoice);
        $formattedInvoice['recipientInfo'] = $this->addRecipientInfo();

        // Return the formatted invoice details as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $formattedInvoice,
        ]);
    }

        /**
     * Add invoice Recipient Info
     */
    public function addRecipientInfo(){
        $recipientInfo = (object)[
            'namePro' => $this->currentSeller->name_pro,
            'fullname' => $this->currentSeller->fullname,
            'registerNumber' => $this->currentSeller->register_number,
            'iceNumber' => $this->currentSeller->ice_number,
            'taxIdentification' => $this->currentSeller->tax_identification,
            'address' => $this->currentSeller->address,
        ];
        return $recipientInfo;
    }
}