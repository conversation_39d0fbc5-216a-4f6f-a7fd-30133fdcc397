<?php
namespace App\Services\Fields;

use GlobalHelper;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\File;

class FieldsService{

    /**
     * Default Columns
     */
    public function DefaultColumns($type,$data){
        // Formatter Data
        $custom_columns = $data['columns'] ?? [];
        if(!$custom_columns){
            $allFields = $this->GetListFields($type,$data);
            $data['columns'] = $allFields['custom_columns'];
            $data['moreInfos'] = $allFields['moreInfos'];
        }

        // Return data
        return $data;
    }

    /**
     * Generate Response Results
     */
    public function ResponseResults($options = []){
        // Initial Params
        $results = $options['results'] ?? [];
        $noPaginate = $options['noPaginate'] ?? NULL;
        $first = $options['first'] ?? NULL;
        $type = $options['type'] ?? NULL;
        $layout = $options['layout'] ?? NULL;
        $resultFormatted = $options['resultFormatted'] ?? NULL;
        $initialResults = $options['initialResults'] ?? $results;

        // Formatter Results
        if($resultFormatted != 1){
            $results = $this->FormatterResults([
                "items" => $first == 1 ? [$results] : ($noPaginate ? $results : $results->items()),
                'first' => $first,
                'layout' => $layout,
                'type' => $type,
            ]);
        }

        // Return Result wihtout paginate
        if ($noPaginate == 1) {
            // Check if $results is an array and contains 'items'
            $results = (is_array($results) && isset($results['items'])) ? $results['items'] : $results;

            // Convert to object for further manipulation
            $results = json_decode(json_encode($results));

            // Handle initial results if $first is true
            if ($initialResults && $first) {
                if(is_array($results)){
                    $results = $results[0] ?? $results;
                }
            }
        } else {
            // Pagination handling
            $results['count'] = $initialResults->total();
            $results['currentPage'] = $initialResults->currentPage();
            $results['totalPages'] = $initialResults->lastPage();
        }

        // Return Result
        return $results;
    }


    /*
        Formatter Fields
    */
    private function FormtterFields($options = []){
        // Get params
        $format = $options['format'] ?? null;
        $fields = $options['fields'] ?? null;

        // Initial Params
        $results = $fields;

        // Formatter Results
        if($format && $fields){

            $results = [];

            // indexed By DB Name
            if($format == "IndexDbName"){
                foreach ($fields as $filed_name => $field_infos) {
                    if($field_infos['dbName'] ?? NULL){
                        $results[$field_infos['dbName']] = $filed_name;
                    }
                }
            }

            // Get DB Name
            if($format == "GetDbName"){
                foreach ($fields as $filed_name => $field_infos) {
                    $results[] = $field_infos['dbName'];
                }
            }
        }

        // Return Result
        return $results;
    }

     /**
     * Formatter Results
     */
    public function FormatterResults($options = []){
        // Get Params
        $new_results = [];
        $first = $options['first'] ?? NULL;
        $items = $options['items'] ?? NULL;
        $type = $options['type'] ?? NULL;
        $new_results['items'] = [];

        // Convert to array
        $items = GlobalHelper::ConvertToArray([
            "liste" => $items,
        ]);

        // Lopp List of rows
        if(is_array($items) && count($items) > 0){
            foreach($items as $row_result){
                // Grouped Result
                $new_result = $this->GroupeFields($type,$row_result);

                // Append to new rows
                $new_results['items'][] = $new_result;
            }
        }

        // get First
        if($first == 1){
            $new_results = $new_results['items'][0] ?? NULL;
        }

        // Return result
        return $new_results;
    }

    /**
     * get liste fields
     */
    public function GetListFields($type, $options = []){
        // Get params
        $custom_columns = $options['custom_columns'] ?? NULL;
        $layout = $options['layout'] ?? "general";
        $withoutFormatter = $options['withoutFormatter'] ?? NULL;
        $format = $options['format'] ?? NULL;

        // Check Type
        if($type){
            if($custom_columns){
                $layout = NULL;
            }

            // Get Fields
            $liste_fields = json_decode(file_get_contents(resource_path("fields/" . $type . ".json")), true);

            // Set fields
            $options['fields'] = $liste_fields;

            // Formatter Fields
            $liste_fields = GlobalHelper::ConvertToArray([
                "liste" => $liste_fields,
            ]);

            // Generate By Format
            if($format){
                return $this->FormtterFields([
                    'format' => $format,
                    'fields' => $liste_fields['fields'],
                ]);
            }

            // Return list of fields
            if ($withoutFormatter == 1) {
                return $liste_fields['fields'];
            }

            // Return Result
            return $this->FormatterFields([
                'liste_fields' => $liste_fields,
                'custom_columns' => $custom_columns,
                'layout' => $layout,
            ]);
        }
    }


    /**
     * Formatter Fields
     */
    private function FormatterFields($options){
        // Get params
        $custom_columns = $options['custom_columns'] ?? NULL;
        $custom_columns = $custom_columns ?  explode(',',$custom_columns) : NULL;
        $layout = $options['layout'] ?? NULL;
        $liste_fields = $options['liste_fields'] ?? NULL;

        // Initial Params
        $tablename =  $liste_fields['table'];
        $fields = $liste_fields['fields'];
        $result_columns = [];
        $moreInfos = [];

        // Check Fields
        foreach($fields as $key_field => $row_field){
            // Check is added or not
            $Added = true;
            $field_layout = $row_field['layout'] ?? NULL;

            // Check Fields
            if($custom_columns && !in_array($key_field,$custom_columns)){ $Added = false; }

            // Check Layout
            if($layout && $field_layout){
                $custom_layouts_ar = explode(',',$layout);
                $fields_layouts_ar = explode(',',$field_layout);
                if(count($custom_layouts_ar ?? []) > 0 && count($fields_layouts_ar ?? []) > 0){
                    $Added = false;
                    foreach($custom_layouts_ar as $clayput){
                        if(in_array($clayput,$fields_layouts_ar)){
                            $Added = true;
                        }
                    }
                }
            }

            // Append to results
            if($Added){
                // Table name
                $table_name = $row_field['tableName'] ?? $tablename;

                // Field name
                $dbRAW = $row_field['dbRAW'] ?? NULL;
                if($dbRAW){
                    $field_name = DB::raw($dbRAW);
                }else{
                    $field_name = $table_name.".".$row_field['dbName']." as ".$key_field;
                }

                // Append
                $result_columns[] = $field_name;

                // More Infos
                if($row_field['tableName'] ?? NULL){
                    $moreInfos[] = $table_name;
                }

            }
        }

        // Generate Result
        $results = [
            'custom_columns' => $result_columns,
            'moreInfos' => array_unique($moreInfos ?? []),
        ];

        // Return Ressult
        return $results;
    }

    /**
     * Groupe Fields
     */
    public function GroupeFields($type, $origin_row) {
        // If origin_row is not an array, return it directly
        if (!is_array($origin_row)) {
            return $origin_row;
        }

        // Get Custom Columns
        $liste_fields = $this->GetListFields($type, [
            'withoutFormatter' => 1,
        ]);

        // Initial new object
        $newRow = [];

        // get keys object
        $keys_result = array_keys($origin_row);

        // Check list of keys
        foreach($keys_result as $kr) {
            // Get Info field
            $grouped_name = $liste_fields[$kr]['grouped'] ?? NULL;
            $key_grouped = $liste_fields[$kr]['key_grouped'] ?? $kr;
            $value_field = $origin_row[$kr];

            // Check is date
            $isDate = GlobalHelper::CheckIsDate(["value" => $value_field, "key" => $key_grouped]);
            if($isDate) {
                $value_field = GlobalHelper::CleanDate($value_field);
            }

            // Make new result
            if($grouped_name) {
                $newRow[$grouped_name][$key_grouped] = $value_field;
            } else {
                $newRow[$key_grouped] = $value_field;
            }
        }

        // Return result
        return $newRow;
    }

    /**
    * Process Data based on the given type and option.
    */
    public function ProcessData($options = []){
        // Get Liste Fields
        $listfields = $options['listfields'] ?? [];
        $AppendParams = $options['AppendParams'] ?? [];
        $type = $options['type'] ?? null;

        // get Fields
        if($type){
            $listfields = $this->GetListFields($type,['withoutFormatter' => 1]);
        }
        // Get option
        $option = $options['option'] ?? NULL; /** store, update */

        // Get Data
        $data = $options['data'] ?? [];

        // Initial Data
        $newData = [];
        // Process data based on the 'data' type
        foreach ($listfields as $field => $config) {
            // Get excludeIn
            $excludeIn = $config['excludeIn'] ?? [];

            // Get Value from data
            $fieldValue = $data[$field] ?? NULL;

            // Check if the field should be excluded based on 'excludeIn'
            if (!in_array($option, $excludeIn) && array_key_exists($field, $data)) { // !is_null($fieldValue)
                if($config['dbName'] ?? NULL){
                    $newData[$config['dbName']] = $fieldValue;
                }
            }
        }

        // Append Params
        if($AppendParams){foreach($AppendParams as $kp => $vp){
            if(!is_null($kp) && !is_null($vp)){
                $newData[$kp] = $vp;
            }
        }}

        // Return Result
        return $newData;
    }

    /**
    * validate Data based on the given type and option.
    */
    public function validateData($options = []){
        // Get Params
        $listfields = $options['listfields'] ?? [];
        $option = $options['option'] ?? NULL; /**createValidation, updateValidation */
        $data = $options['data'] ?? [];
        $messages = $options['messages'] ?? [];
        $type = $options['type'] ?? null;
        $replaceParams = $options['replaceParams'] ?? [];

        // get Fields
        if($type){
            $listfields = $this->GetListFields($type,['withoutFormatter' => 1]);
        }

        // Initial Params
        $rules = [];

        // Exclude fields specified in the 'excludeIn' array
        foreach ($listfields as $field => $config) {

            // Get excludeIn
            $excludeIn = $config['excludeIn'] ?? [];

            // Check Exclude In
            if (!in_array($option, $excludeIn) && !empty($config[$option])) {
                $rules[$field] = $config[$option];

                // checking for conditional validation
                if(str_contains($rules[$field],"{")){
                    $rules[$field] = str_replace(array_keys($replaceParams),array_values($replaceParams),$rules[$field]);

                }

            }
        }

        // Return a Validator instance to validate the data
        $validator = Validator::make($data, $rules,$messages);
        // Check if data validation fails
        if ($validator->fails()) {
            return [
                'response' => 'error',
                'message' => implode(",",$validator->messages()->all()),
            ];
        }else{
            return [
                'response' => 'success',
            ];
        }
    }

    /**
     * Format data based on the JSON configuration file.
     *
     * @param array $options
     * @return array
     */
    public function formatData(array $options): array
    {
        $type = $options['type'] ?? null;
        $data = $options['data'] ?? [];

        if (!$type || !$data) {
            return [];
        }

        // Load the JSON configuration file
        $filePath = resource_path("fields/{$type}.json");
        if (!File::exists($filePath)) {
            return [];
        }

        $config = json_decode(File::get($filePath), true);
        $fields = $config['fields'] ?? [];

        // Map the data based on the configuration
        $formattedData = [];
        foreach ($fields as $key => $field) {
            $dbName = $field['dbName'] ?? null;
            if ($dbName && isset($data[$dbName])) {
                $formattedData[$key] = $data[$dbName];
            }
        }

        return $formattedData;
    }
}