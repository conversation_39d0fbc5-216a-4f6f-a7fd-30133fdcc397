<?php

namespace App\Http\Controllers\Kpis;

use App\Http\Controllers\Controller;
use App\Services\Kpis\KpisFunds;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use App\Models\FacturesSellers;
use SellersHelper;

class FundsController extends Controller{
    protected KpisFunds $KpisFunds;
    protected $currentSeller;

    /**
     * Inject the FilterOrders service into the controller
     */
    public function __construct(){
        $this->KpisFunds = new KpisFunds();
        $this->currentSeller = SellersHelper::CurrentSeller();
    }

    /**
     * Funds Kpi
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function funds(Request $request): JsonResponse{
        // Validate the request
        $validateData = $this->validateData($request);
        if ($validateData['response'] === 'error') { return response()->json($validateData, 400); }

        $fundsKpis = $this->KpisFunds->{'funds' . ucfirst($request->type)}($request->all());
        
        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $fundsKpis,
        ]);
    }

     /**
     * Validate
     */
    private function validateData(Request $request): ?array{
        // Validate the uploaded file using Laravel's Validator
        $validator = Validator::make($request->all(), [
            'type' => ['required', Rule::in(['all','confirmed','delivered'])]
        ]);

        // Check if validation fails
        if ($validator->fails()) {
            return [
                'response' => 'error',
                'message'  => implode(", ", $validator->messages()->all()),
            ];
        }

        // Return success response if validation passes
        return [
            'response' => 'success',
        ];
    }

    /**
     * Profit KPI
     *
     * @param Request $request
     * @param int $year
     * @return JsonResponse
     */
    public function profits(Request $request, int $year): JsonResponse{
        $data = FacturesSellers::selectRaw('MONTH(created_at) as month')
            ->selectRaw('ROUND(SUM(total_reste), 2) as total_reste, ROUND(SUM(total_comission), 2) as total_comission')
            ->whereYear('created_at', $year)
            ->where('seller_id',$this->currentSeller->id)
            ->groupBy('month')
            ->orderBy('month')
            ->get();


        // Return Response
        return response()->json([
            'response' => 'success',
            'result' => [
                'profits' => $data->pluck('total_reste', 'month'),
                'charges' => $data->pluck('total_comission', 'month'),
            ],
        ]);
    }
    
}