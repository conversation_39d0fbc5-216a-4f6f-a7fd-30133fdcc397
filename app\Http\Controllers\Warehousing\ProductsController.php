<?php

namespace App\Http\Controllers\Warehousing;

use App\Http\Controllers\Controller;
use App\Models\Medias;
use App\Models\SellersStock;
use App\Models\SellersStockOperations;
use App\Services\Shipping\FilterCountries;
use App\Services\Validator\ProductsValidator;
use App\Services\Warehousing\FilterProducts;
use App\Services\Warehousing\InventoryService;
use App\Services\Warehousing\ProductsService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class ProductsController extends Controller{
    protected $FilterProducts;
    protected $ProductsValidator;
    protected $ProductsService;
    protected $FilterCountries;
    protected $InventoryService;

    /**
     * Inject the FilterProducts service into the controller
     */
    public function __construct(){
        $this->FilterProducts = new FilterProducts();
        $this->ProductsValidator = new ProductsValidator();
        $this->ProductsService = new ProductsService();
        $this->FilterCountries = new FilterCountries();
        $this->InventoryService = new InventoryService();
    }

    /**
     * Retrieve and return a list of filtered orders.
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function list(Request $request): JsonResponse{
        // return response()->json(Medias::where("table_name",'sellers_stock')
        //     ->get());

        // Define the filters with the 'parent' set to 'noparent' by default
        $filters = $request->all();
        $filters['parent'] = 'noparent';

        // Retrieve the list of products based on the defined filters
        $result = $this->FilterProducts->getProducts($filters);

        // Prepare the base response
        $results = [
            'response' => 'success',
            'result' => $request->count ? $result : ($result['items'] ?? []),
        ];

        if (!$request->count) {
            $results['paginate'] = [
                'count' => $result['count'] ?? 0,
                'currentPage' => $result['currentPage'] ?? 1,
                'totalPages' => $result['totalPages'] ?? 1,
            ];
        }
        return response()->json($results);
    }

    /**
     * Product details
    */
    public function details(Request $request,$sku){
         $id = $this->getIdFromSku($sku);
        // Validate Product
        $validateProduct = $this->ProductsValidator->validateProduct(['id' => $id,'layout' => 'details',"with" => ["salesPrices.prices","upsell.prices"]]);
        if($validateProduct['response'] == 'error'){ return response()->json($validateProduct, 404); }
        //get list of all product ids
        $allProductSku = $this->FilterProducts->getProducts([
            'parent' => 'noparent',
            'noPaginate' => 1,
            'columns' => ['reference'],
            'noFormatter' => true
        ])->pluck('reference')->all();
        // Get Product
        $rowProduct = $validateProduct["rowProduct"] ?? [];

        // Retrieves product variants
        $rowProduct = $this->productVariantes($rowProduct);

        // Formatter Sales Prices
        $rowProduct = $this->formatterSalesPrices($rowProduct);

        // Formatter Upsell
        $rowProduct = $this->formatterUpsell($rowProduct);

        $rowProduct->allProductSku = $allProductSku;
        $rowProduct->availableCountries = $this->InventoryService->getStockCountries($rowProduct);


        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $rowProduct
        ]);
    }

    //the product will be verified after in getProducts
    private function getIdFromSku( $sku)
    {
        return SellersStock::where('reference', $sku)->value('id');
    }

    /**
     * Product inventory
    */
    public function inventory(Request $request,$sku){
        $id = $this->getIdFromSku($sku);
        // Validate Product
        $validateProduct = $this->ProductsValidator->validateProduct(['id' => $id,'layout' => 'details']);
        if($validateProduct['response'] == 'error'){ return response()->json($validateProduct, 404); }
        $countryId = $request->query('countryId');
        // Get Product
        $rowProduct = $validateProduct["rowProduct"] ?? [];

        // Load Inventory
        $inventoryDetails = $this->InventoryService->inventoryMovement($rowProduct,$countryId);

        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => [
                // 'product' => $rowProduct,
                'inventory' => $inventoryDetails,
            ],
        ]);
    }


    /**
     * List of types
     */
    public function types(){
        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $this->ProductsService->getProductTypes(),
        ]);
    }

    /**
     * List of categories
     */
    public function categories(){
         // Return the response as a JSON object
         return response()->json([
            'response' => 'success',
            'result' => $this->ProductsService->getProductCategories(),
        ]);
    }

    /**
     * Retrieves product variants if the product is of type "variable".
     */
    private function productVariantes(object $rowProduct): object{
        // Check if the product type is "variable"
        if ($rowProduct->productType !== 'variable') {
            return $rowProduct;
        }

        // Fetch and assign product variants
        $rowProduct->variants = $this->FilterProducts->getProducts([
            'parent' => $rowProduct->id,
            'layout' => 'variantes',
            'noPaginate' => 1,
        ]);

        return $rowProduct;
    }

    /**
     * Format Sales Prices for a given product.
     */
    private function formatterSalesPrices($rowProduct){
        // Check if sales_prices exists, then rename it to salesPrices
        if (isset($rowProduct->sales_prices)) {
            $rowProduct->salesPrices = collect($rowProduct->sales_prices)->map(function ($offer) {
                // Offer Prices
                $prices = isset($offer->prices) ? collect($offer->prices)->map(function ($price) {
                    return [
                        'price' => $price->price,
                        'currency' => $price->currency,
                    ];
                })->toArray() : [];

                return [
                    'id' => $offer->id,
                    'name' => $offer->offer_name,
                    'quantityFree' => $offer->total_free,
                    'quantityPaid' => $offer->total_paid,
                    'prices' => $prices,
                ];
            })->toArray();

            // Remove the old sales_prices property
            unset($rowProduct->sales_prices);
        }

        return $rowProduct;
    }
    /**
     * Format Upsell Prices for a given product.
     */
    private function formatterUpsell($rowProduct){
        // Check if upsell exists, then rename it to upsellPrices
        if (isset($rowProduct->upsell)) {
            $rowProduct->upsell = collect($rowProduct->upsell)->map(function ($upsell) {
                // Check if 'prices' exists within the upsell
                $prices = isset($upsell->prices) ? collect($upsell->prices)->map(function ($price) {
                    return [
                        'price' => $price->price,
                        'currency' => $price->currency,
                    ];
                })->toArray() : [];

                return [
                    'id' => $upsell->id,
                    'name' => $upsell->upsell_name,
                    'quantityFree' => $upsell->total_free,
                    'quantityPaid' => $upsell->total_paid,
                    'prices' => $prices,
                ];
            })->toArray();
        }

        return $rowProduct;
    }
}