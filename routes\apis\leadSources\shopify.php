<?php
use App\Http\Controllers\LeadSources\ShopifyImportsController;
use Illuminate\Support\Facades\Route;


Route::prefix('shopify')->name('shopify.')->group(function () {

    Route::get('/auth/install', [ShopifyImportsController::class, 'install'])->name('shopify.install');
    Route::get('/auth/callback', [ShopifyImportsController::class, 'callback'])->name('shopify.callback');

    Route::get('/orders', [ShopifyImportsController::class, 'getOrders'])->middleware('auth.token');
    Route::post('/addSourceLead', [ShopifyImportsController::class, 'addShopifySourceLead'])->middleware('auth.token');
    Route::post('/syncCodStatus', [ShopifyImportsController::class, 'syncCodStatus'])->middleware('auth.token');

    //must be public and open
    Route::post('/orders/create/{id}', [ShopifyImportsController::class, 'handleOrderWebhook'])->name('webhook');
    Route::get('/customers/data_request', [ShopifyImportsController::class, 'customerDataRequest'])->middleware('auth.token');
    Route::get('/customers/redact', [ShopifyImportsController::class, 'customerDataErasure'])->middleware('auth.token');
    Route::get('/shop/redact', [ShopifyImportsController::class, 'shopDataErasure'])->middleware('auth.token');
});