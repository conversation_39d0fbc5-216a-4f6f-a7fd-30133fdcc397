<?php

namespace App\Http\Controllers\Kpis;

use App\Http\Controllers\Controller;
use App\Models\SellersColis;
use App\Services\Kpis\KpisConfirmation;
use App\Services\Kpis\KpisFollowup;
use App\Services\Kpis\KpisShipping;
use App\Services\Orders\OrderStatusService;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use SellersHelper;

class KpisController extends Controller{
    protected KpisConfirmation $KpisConfirmation;
    protected KpisShipping $KpisShipping;
    protected KpisFollowup $KpisFollowup;
    protected OrderStatusService $OrderStatusService;
    protected $currentSeller;

    /**
     * Inject the FilterOrders service into the controller
     */
    public function __construct(){
        $this->KpisConfirmation = new KpisConfirmation();
        $this->KpisShipping = new KpisShipping();
        $this->KpisFollowup = new KpisFollowup();
        $this->OrderStatusService = new OrderStatusService();
        $this->currentSeller = SellersHelper::CurrentSeller();
    }

    /**
     * Confirmations Kpi
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function confirmation(Request $request): JsonResponse{
        $confirmationKpis = $this->KpisConfirmation->confirmationKpis($request->all());

        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $confirmationKpis,
        ]);
    }

    /**
     * Shipping Kpi
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function shipping(Request $request): JsonResponse{
        $shippingKpis = $this->KpisShipping->shippingKpis($request->all());

        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $shippingKpis,
        ]);
    }

    /**
     * Followup Kpi
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function followup(Request $request): JsonResponse{
        $followupKpis = $this->KpisFollowup->followupKpis($request->all());

        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $followupKpis,
        ]);
    }

    /**
     * Delivered Rate KPI
     *
     * @param Request $request
     * @param int $year
     * @return JsonResponse
     */
    public function delevredRate(Request $request, int $year): JsonResponse{
        $data = SellersColis::select(DB::raw('MONTH(delivered_at) as month'), DB::raw('COUNT(*) as total'))
            ->where('statut_colis', 'delivered')
            ->whereYear('delivered_at', $year)
            ->where('seller_id',$this->currentSeller->id)
            ->groupBy(DB::raw('MONTH(delivered_at)'))
            ->orderBy(DB::raw('MONTH(delivered_at)'))
            ->pluck('total', 'month');

        return response()->json([
            'response' => 'success',
            'result' => $data,
        ]);
    }
}