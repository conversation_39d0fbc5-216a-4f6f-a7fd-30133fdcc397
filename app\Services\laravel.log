[2025-08-25 11:14:55] local.ERROR: Undefined array key "result" {"exception":"[object] (ErrorException(code: 0): Undefined array key \"result\" at /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/app/Http/Controllers/LeadSources/ShopifyImportsController.php:321)
[stacktrace]
#0 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Foundation/Bootstrap/HandleExceptions.php(256): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError()
#1 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/app/Http/Controllers/LeadSources/ShopifyImportsController.php(321): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->Illuminate\\Foundation\\Bootstrap\\{closure}()
#2 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php(47): App\\Http\\Controllers\\LeadSources\\ShopifyImportsController->handleOrderWebhook()
#3 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Routing/Route.php(266): Illuminate\\Routing\\ControllerDispatcher->dispatch()
#4 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Routing/Route.php(212): Illuminate\\Routing\\Route->runController()
#5 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Routing/Router.php(808): Illuminate\\Routing\\Route->run()
#6 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}()
#7 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php(51): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#8 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle()
#9 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#10 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Routing/Router.php(807): Illuminate\\Pipeline\\Pipeline->then()
#11 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Routing/Router.php(786): Illuminate\\Routing\\Router->runRouteWithinStack()
#12 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Routing/Router.php(750): Illuminate\\Routing\\Router->runRoute()
#13 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Routing/Router.php(739): Illuminate\\Routing\\Router->dispatchToRoute()
#14 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(201): Illuminate\\Routing\\Router->dispatch()
#15 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(170): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}()
#16 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#17 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#18 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle()
#19 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#20 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php(51): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle()
#21 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle()
#22 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Http/Middleware/ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#23 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\ValidatePostSize->handle()
#24 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php(110): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#25 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle()
#26 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php(62): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#27 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\HandleCors->handle()
#28 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php(58): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#29 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Http\\Middleware\\TrustProxies->handle()
#30 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/InvokeDeferredCallbacks.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#31 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(209): Illuminate\\Foundation\\Http\\Middleware\\InvokeDeferredCallbacks->handle()
#32 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}()
#33 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(176): Illuminate\\Pipeline\\Pipeline->then()
#34 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php(145): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter()
#35 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/vendor/laravel/framework/src/Illuminate/Foundation/Application.php(1220): Illuminate\\Foundation\\Http\\Kernel->handle()
#36 /var/www/vhosts/codpowergroup.dev/dev-api-seller.codpowergroup.dev/public/index.php(17): Illuminate\\Foundation\\Application->handleRequest()
#37 {main}
"} 
