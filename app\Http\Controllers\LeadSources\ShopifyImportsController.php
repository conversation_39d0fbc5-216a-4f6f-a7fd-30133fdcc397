<?php

namespace App\Http\Controllers\LeadSources;

use App\Enums\CodStatus;
use App\Http\Controllers\Controller;
use App\Models\Sellers;
use App\Models\SellersApiImports;
use App\Models\SellersColis;
use App\Models\SellersColisMeta;
use App\Models\User;
use App\Services\LeadSource\LeadSourceService;
use App\Services\LeadSource\StatusFlow\ShopifyFulfillFlow;
use App\Services\LeadSource\ShopifyService;
use App\Services\Orders\OrderAPIService;
use App\Services\Orders\OrderStatusService;
use App\Services\Sellers\FilterSellers;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use SellersHelper as GlobalSellersHelper;

class ShopifyImportsController extends Controller
{
    protected $shopifyService;
    protected $currentSeller;
    protected $leadSourceService;
    protected $OrderAPIService;
    protected $filterSelleres;
    protected $StatusFlow;

    public function __construct()
    {
        $this->currentSeller = GlobalSellersHelper::CurrentSeller();
        $this->shopifyService = new ShopifyService();
        $this->leadSourceService = new LeadSourceService();
        $this->OrderAPIService = new OrderAPIService();
        $this->filterSelleres = new FilterSellers();
        $this->StatusFlow = new ShopifyFulfillFlow();
    }
    /**
     * Install Shopify App - Redirect to authorization page.
     */
    public function install(Request $request)
    {
        $params = $request->query();

        // Step 1: Verify HMAC & session
        if ($this->hasHmac($params,2)) {
            $res = $this->AlreadyInstalled( $params);
            return $res;
        }

        // Step 3: Verify only HMAC (only HMAC means the install come from shopify app store directly)
        if ($this->hasHmac($params,1)) {

            $res = $this->InstallFromShopifyAppStore($request);
            return $res;
        }

        // No HMAC means first installation request from Seller ERP
        $validationResponse = $this->shopifyService->validateRequestParameters($request, ['storeUrl']);
        $data = $validationResponse->getData(true);

        if ($data['response'] === 'error') {
            return $validationResponse;
        }

        //build install url
        $shop = $data['result']['storeUrl'];
        $installUrl = $this->buildInstallUrl($shop);

        if ($request->wantsJson() || $request->query('json') == 1) {
            return response()->json([
                'response' => 'success',
                'result' => $installUrl,
            ]);
        } else {
            return redirect($installUrl);
        }
    }



    public function syncCodStatus(Request $request)
    {
        $request->validate([
            'orderId' => 'required|integer',
            'status'   => 'required|string', // can be 'waiting-pick-up' or 'confirmed' or 'processing' etc.
            'ctx'      => 'sometimes|array',
        ]);

        // get the shopify order id from the meta data
        $OrderMeta = SellersColisMeta::where('order_id', $request->input('orderId'))
            ->where('meta_key', 'shopify_order_id')
            ->first();

        if (!$OrderMeta) {
            return response()->json([
                'response' => 'error',
                'message' => 'Shopify order id not found',
            ], 400);
        }

        $result = $this->StatusFlow->syncStatusBySeller(
            sellerId: $this->currentSeller->id,
            orderId:  (int) $OrderMeta->meta_value,
            statusName: (string) $request->input('status'),
            ctx: $request->input('ctx', [])
        );

        return response()->json($result, $result['response'] === 'success' ? 200 : ($result['status'] ?? 400));
    }


    /**
     * Handle OAuth Callback - Get Access Token and Register Webhooks
     */
    public function callback(Request $request)
    {
        // Validate request parameters
        $validationResponse = $this->shopifyService->validateRequestParameters($request, ['shop', 'code']);
        $data = $validationResponse->getData(true);

        if ($data['response'] === 'error') {
            return $validationResponse; // Return error response if validation failed
        }

        // Proceed with the validated parameters
        $shop = $data['result']['shop'];
        $code = $data['result']['code'];
        // Exchange code for access token
        $accessTokenResponse = $this->shopifyService->getAccessToken($shop, $code);


        // Decode the JSON response
        $accessTokenData = $accessTokenResponse->getData(true);

        // Check if an error occurred
        if ($accessTokenData['response'] === 'error') {
            return $accessTokenResponse; // Return the error response
        }

        $baseUrl =env('URL_APP_SELLERS').'/lead-sources';

        $redirectUrl = $baseUrl . '?name=shopify&shop=' . urlencode($shop) . '&token=' . urlencode($accessTokenData['result']);

        return redirect()->away($redirectUrl);
    }


    /**
     * Add a Shopify lead source for the current seller.
     *
     * @param \Illuminate\Http\Request $request The incoming HTTP request containing the required parameters.
     *
     * @return \Illuminate\Http\JsonResponse A JSON response indicating success or failure.
     *
     * @throws \Illuminate\Database\Eloquent\ModelNotFoundException If the seller is not found.
     */
    public function addShopifySourceLead(Request $request)
    {
        // Validate required form data parameters 'token' and 'shop'
        $request->validate([
            'token' => 'required|string',
            'shop'  => 'required|string',
        ]);

        // Get validated inputs
        $token = $request->input('token');
        $shop = $request->input('shop');

        // Get seller
        $seller = Sellers::find($this->currentSeller->id);
        if (!$seller) {
            return response()->json([
                'response' => 'error',
                'message' => 'Seller not found',
            ], 404);
        }
            // Register webhooks
           $webhooks = $this->registerWebhooks($shop, $token, $seller->id);

        $this->leadSourceService->createLeadSourceToken([
            'api_name' => 'shopify',
            'api_token' => $token,
            'seller' => $seller,
            'api_label' => $shop,
            'shopurl' => $shop,
            'webhook' => json_encode($webhooks),
        ]);

        return response()->json([
            'response' => 'success',
            'message' => 'Shopify lead source added successfully and webhooks registered.',
        ], 200);
    }

    /**
     * Register webhooks for the given topics.
     *
     * @param string $shop
     * @param string $accessToken
     * @param string $sellerId
     * @return array
     */
    protected function registerWebhooks($shop, $accessToken, $sellerId)
    {
        $webhookUrl = route('shopify.webhook', ['id' => $sellerId]); //url('/api/v1'). "/woocommerce/order/created/".$this->currentSeller->id;

        // Define all webhook topics
        $webhookTopics = [
            'orders/create',
            'app/uninstalled'
        ];
        $result = [];
        foreach ($webhookTopics as $topic) {
            $webhookResponse = Http::withHeaders([
                'X-Shopify-Access-Token' => $accessToken
            ])->post("https://{$shop}/admin/api/2025-01/webhooks.json", [
                'webhook' => [
                    'topic' => $topic,
                    'address' => $webhookUrl,
                    'format' => 'json'
                ]
            ]);

         $webhookData = $webhookResponse->json();

            if (!isset($webhookData['webhook'])) {
                Log::error(" Failed to register webhook for {$topic} ".url('/api/v1') . "/shopify/" . $topic.'/'.$sellerId,  $webhookData);
            }else{
                array_push($result, $webhookData['webhook']);
            }
        }
        return $result;
    }

    /**
     * Verify HMAC and match shop to seller.
     */
    public function InstallFromShopifyAppStore(Request $request)
    {
        $validationResponse = $this->shopifyService->validateRequestParameters($request, ['shop']);
        $data = $validationResponse->getData(true);

        if ($data['response'] === 'error') {
            return $validationResponse;
        }

        $shop = $data['result']['shop'];
        $installUrl = $this->buildInstallUrl($shop);

        if ($request->wantsJson() || $request->query('json') == 1) {
            return response()->json([
                'response' => 'success',
                'result' => $installUrl,
            ]);
        } else {
            return redirect($installUrl);
        }
    }
    /**
     * Verify HMAC and match shop to seller.
     */
    public function AlreadyInstalled($params)
    {
        // Remove 'json' from params if present
        if (isset($params['json'])) {
            unset($params['json']);
        }

        if (!$this->verifyHmac($params)) {
            return response()->json([
                'response' => 'error',
                'message' => __(key: 'sourcing.request_not_authorized'),
            ], 401);
        }
        $shop = $params['shop'] ?? null;
        if ($shop) {
            // Step 2: Match shop to seller
            $res = $this->handleShopifyAuth($shop);
            if ($res['response'] === 'error') {
                return response()->json($res, 400);
            }
            return response()->json($res, 200);
        }

        return response()->json([
            'response' => 'success',
            'result' => env('URL_APP_SELLERS') . '/lead-sources',
        ]);
        }

     /**
     * Handle Order Webhook
     */
    public function handleOrderWebhook(Request $request,$id)
    {

        $request_from_shopify = $this->shopifyService->validateShopifyRequest($request);
        // save it same why as excel order
        if ($request_from_shopify !== true) {


            return $request_from_shopify; // HACKER possibility here
        }
        if($request->header('X-Shopify-Topic') == 'app/uninstalled'){
            $res = $this->handleAppUninstalled($request,$id);
            if($res['response'] === 'error'){
                return response()->json($res, 400);
            }
            return response()->json($res, 200);
        }else{
            $storeDomain = $request->header('X-Shopify-Shop-Domain');


            // Formatter Data
            $orderData = $this->shopifyService->formatWebHookOrder([
                'store_name' => $storeDomain,
                ...$request->all()
            ]);
            // Send Order via api
            $responseOrders = $this->OrderAPIService->sendOrders([
                'orderData' => $orderData,
                'sellerId' => $id
            ]);

            // Check if the response contains an error
            if ($responseOrders['response'] === 'error') {
                return response()->json($responseOrders, 400);
            }

            Log::info('AddOrderMetaData', ['orderCode' => $responseOrders['orderCode'], 'storeDomain' => $storeDomain]);
            $MetaDataSaved =  $this->AddOrderMetaData($responseOrders['orderCode'],'shopify_order_id',$request->input('id'),$storeDomain);

            if(!$MetaDataSaved){
                return response()->json([
                    'response' => 'error',
                    'message' => 'Failed to add meta data',
                ], 400);
            }
            // set lastImport date
            $this->updateShopifyLastImportDate($id, $storeDomain, count($orderData));

            // Return Response
            return response()->json($responseOrders, 200 );
        }

    }

    /**
     * Retrieves a list of orders from the Shopify store.
     *
     * @param Request $request The incoming request instance.
     * @return \Illuminate\Http\JsonResponse The JSON response containing the list of orders or an error message.
     */
    public function getOrders(Request $request)
    {
        // Check if the Shopify access token is present in the session
        $tokenResponse = $this->shopifyService->checkShopifyToken();
        $data = $tokenResponse->getData(true);

        if ($data['response'] === 'error') {
            return $tokenResponse; // Return the error response if the token is missing
        }

        $accessToken = $data['result']; // Extract the access token if successful

        // Validate request parameters
        $validationResponse = $this->shopifyService->validateRequestParameters($request, ['shop', 'code']);
        $data = $validationResponse->getData(true);

        if ($data['response'] === 'error') {
            return $validationResponse; // Return error response if validation failed
        }
        $shop = $data['result']['shop'];

        $response = Http::withHeaders([
            'X-Shopify-Access-Token' => $accessToken
        ])->get("https://{$shop}/admin/api/2023-10/orders.json");

        if ($response->failed()) {
            return response()->json([
                'response' => 'error',
                'message' => __('sourcing.failed_to_fetch_orders_from_shopify'),
            ], 500);
        }

        return response()->json([
            'response' => 'success',
            'result' => $response->json(),
        ],200);
    }

    /**
     * Handle customer data request from Shopify.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */

    public function customerDataRequest(Request $request)
    {
       // Validate the Shopify request
        $validationResponse = $this->shopifyService->validateShopifyRequest($request);
        $data = $validationResponse->getData(true);

        if ($data['response'] === 'error') {
            return $validationResponse; // Return the error response if validation failed (potential hacker)
        }

        // Process the request as needed
        return response()->json(['message' => 'Customer data request received'], 200);
    }

    /**
     * Handle customer data erasure request from Shopify.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */

    public function customerDataErasure(Request $request)
    {
        // Validate the Shopify request
        $validationResponse = $this->shopifyService->validateShopifyRequest($request);
        $data = $validationResponse->getData(true);

        if ($data['response'] === 'error') {
            return $validationResponse; // Return the error response if validation failed (potential hacker)
        }

        return response()->json(['message' => 'Customer data erasure request received'], 200);
    }

    /**
     * Handle shop data erasure request from Shopify.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */

    public function shopDataErasure(Request $request)
    {
       // Validate the Shopify request
       $validationResponse = $this->shopifyService->validateShopifyRequest($request);
       $data = $validationResponse->getData(true);

       if ($data['response'] === 'error') {
           return $validationResponse; // Return the error response if validation failed (potential hacker)
       }

        return response()->json(['message' => 'Shop data erasure request received'], 200);
    }

    /**
     * Handle App Uninstalled Webhook
     */
    public function handleAppUninstalled(Request $request,$id)
    {
        //get myshopify_domain from request
        $myshopify_domain = $request->header('X-Shopify-Shop-Domain');

        $this->leadSourceService->deleteLeadsSourceAccount([
            'api_name' => 'shopify',
            'seller_id' => $id,
            'shopurl' => $myshopify_domain,
        ]);

        return [
            'response' => 'success',
        ];
    }

    /**
     * Add Order Meta Data
     */
    public function AddOrderMetaData($orderCode,$key,$value,$storeDomain)
    {   Log::info('AddOrderMetaData', ['orderCode' => $orderCode, 'data' => SellersColis::where('order_num', $orderCode)
        ->where('store_name', $storeDomain)
        ->first()]);
        // get the order id from order code
        // I didnt use getOrder, because i need to filter by store and num
         $orderData = SellersColis::where('order_num', $orderCode)
         ->where('store_name', $storeDomain)
         ->first();
        if(!$orderData){
            Log::error('Order not found', ['orderCode' => $orderCode, 'storeDomain' => $storeDomain]);
            return false;
        }

        $metaData = [
            'metaKey' => $key,
            'metaValue' => $value,
        ];
        $res = $this->leadSourceService->createOrderMetaData($orderData->id,$orderData->order_num,$metaData);
        if($res){
            return true;

        }else{

            Log::error('Order Meta Data not added', ['orderCode' => $orderCode, 'storeDomain' => $storeDomain]);
            return false;
        }
    }


    /**
     * Update the last import date and order count for Shopify.
     *
     * @param int $sellerId
     * @param string $storeDomain
     * @param int $numberOfOrders
     * @return void
     */
    private function updateShopifyLastImportDate($sellerId, $storeDomain, $numberOfOrders)
    {
        SellersApiImports::where('seller_id', $sellerId)
            ->where('api_name', 'shopify')
            ->where('shopurl', $storeDomain)
            ->update([
                'last_imported_order' => $numberOfOrders,
                'last_imported_date' => now(),
            ]);

    }

    /**
     * Check if the request contains an HMAC signature.
     *
     * @param array $params
     * @return bool
     */
    private function hasHmac(array $params, int $option): bool
    {
        //check hmac and session
        if (isset($params['hmac']) && isset($params['session']) && $option == 2) {
            return true;
        }else if (isset($params['hmac']) && $option == 1) {
            return true;
        }
        return false;
    }
    /**
     * Verify the HMAC signature in the request parameters.
     *
     * @param array $params
     * @return bool
     */
    private function verifyHmac(array $params): bool
    {

        $hmac = $params['hmac'];
        unset($params['hmac']);

        ksort($params);
        $queryString = http_build_query($params, '', '&', PHP_QUERY_RFC3986);

        $calculatedHmac = hash_hmac('sha256', $queryString, env('SHOPIFY_API_SECRET'));

        return hash_equals($hmac, $calculatedHmac);
    }

    /**
     * Build the installation URL for Shopify.
     *
     * @param string $shop
     * @return string
     */
    private function buildInstallUrl(string $shop): string
    {
        $redirectUri = env('SHOPIFY_REDIRECT_URI');
        // $scopes = "read_orders,write_orders,read_products";
        $scopes = "read_orders,write_orders,
        read_fulfillments,write_fulfillments,
        read_merchant_managed_fulfillment_orders,write_merchant_managed_fulfillment_orders,
        read_returns,write_returns";

        return "https://{$shop}/admin/oauth/authorize?"
            . "client_id=" . env('SHOPIFY_API_KEY')
            . "&scope={$scopes}"
            . "&redirect_uri=" . urlencode($redirectUri);
    }

    /**
     * Handle Shopify authentication and auto-login.
     *
     * @param string $shop
     * @return array
     */
    protected function handleShopifyAuth($shop)
    {
        $existingLeadSource = SellersApiImports::where('api_name', 'shopify')
                        ->where('shopurl', $shop)
                        ->first();

        if ($existingLeadSource) {
            // Step 3: Auto-login: generate new access token
            $seller = Sellers::find($existingLeadSource->seller_id);
            if ($seller && $seller->statut == 'enabled' && $seller->statut_validate == 'open') {

                $user = User::findOrFail($seller->user_id);
                if(!$user)
                {
                    return [
                    'response' => 'error',
                    'message' => 'User not found',
                ];
                }
                $token = $user->createToken('token')->accessToken;

                return [
                    'response' => 'success',
                    'result' => [
                        'accessToken' => $token,
                        'user' => $this->filterSelleres->getSellers([
                            "user_id" => $user->id,
                            "first" => 1,
                        ]),
                        'url' => env('URL_APP_SELLERS') . '/lead-sources'
                    ],
                ];
            }
        }
        return [
            'response' => 'error',
            'message' => 'Seller not found',
        ];
    }


}