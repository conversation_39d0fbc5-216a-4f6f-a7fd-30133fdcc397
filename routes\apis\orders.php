<?php

use App\Http\Controllers\Orders\FollowupController;
use App\Http\Controllers\Orders\OrderController;
use App\Http\Controllers\Orders\OrderExportController;
use App\Http\Controllers\Orders\OrderFlowController;
use Illuminate\Support\Facades\Route;

// Manage Orders Routes
Route::prefix('orders')->name('orders.')->group(function () {
    Route::get('list', [OrderController::class, 'list'])->name('list'); // List of orders
    Route::get('statuses', [OrderController::class, 'statuses'])->name('statuses'); // List of statuses
    Route::get('count', [OrderController::class, 'countOrders'])->name('count'); // Count of orders
    Route::get('payments', [OrderController::class, 'payments'])->name('payments'); // List of payments methods
    Route::get('export', [OrderExportController::class, 'export'])->name('export'); // Export Orders
    Route::get('{id}', [OrderController::class, 'details'])->name('details'); // Details of order

    // Flow Order
    Route::get('{id}/customer', [OrderFlowController::class, 'customer'])->name('customer'); // Order Customer
    Route::get('{id}/products', [OrderFlowController::class, 'products'])->name('products'); // Order products
    Route::get('{id}/calls', [OrderFlowController::class, 'calls'])->name('calls')->middleware('auth.permissions:sellers.calls'); // Order calls
    Route::get('{id}/confirmation', [OrderFlowController::class, 'confirmation'])->name('confirmation')->middleware('auth.permissions:sellers.confirmation'); // Order confirmation
    Route::get('{id}/followup', [OrderFlowController::class, 'followup'])->name('followup')->middleware('auth.permissions:sellers.followup'); // Order followup
    Route::get('{id}/shipping', [OrderFlowController::class, 'shipping'])->name('shipping')->middleware('auth.permissions:sellers.shipping'); // Order shipping   

    // Followup Routes
    Route::prefix('followup')->name('followup.')->group(function () {
        Route::get('statuses', [FollowupController::class, 'statuses'])->name('statuses'); // List of statuses
        Route::get('count', [FollowupController::class, 'countOrders'])->name('count'); // Count of orders
    });
});