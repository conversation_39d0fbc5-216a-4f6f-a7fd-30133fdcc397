<?php

namespace App\Http\Controllers\Auth;

use Illuminate\Support\Facades\Crypt;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\Rule;
use SellersHelper;

class SellerApiTokenController
{
    public function getCurrentSeller()
    {
        return SellersHelper::CurrentSeller();
    }
   /**
     * Issue a new API token for the authenticated seller.
     */
    public function issue(Request $req)
    {
        $seller = $this->getCurrentSeller();

        $data = $req->validate([
            'scopes' => ['required','array', Rule::in([
                'orders:read','orders:create','orders:update','orders:followup:cancel',
                'products:read','products:create','products:update',
                'ref:read',
            ])],
        ]);

        $now = now();
        $payload = [
            'ver'    => 1,
            'sid'    => $seller->id,
            'scopes' => $data['scopes'],
            'iat'    => $now->unix(),
            'exp'    => null, // never expires
        ];

        $token = Crypt::encryptString(json_encode($payload));

        // store for revocation/check
        $seller->forceFill(['api_token' => $token])->save();

        return response()->json([
            'response' => 'success',
            'result' => [
                'token'      => $token,   // show once in UI
                'scopes'     => $payload['scopes']
            ],
        ], 201);
    }

    /**
     * Get the authenticated seller's API token.
     */
    public function getCurrentSellerToken()
    {
        $seller = $this->getCurrentSeller();
        return response()->json([
            'response' => 'success',
            'result' => [
                'token' => $seller->api_token,
                'scopes' => json_decode(Crypt::decryptString($seller->api_token), true)['scopes'] ?? []
            ],
        ]);
    }

    /**
     * Revoke the authenticated seller's API token.
     */
    public function revoke(Request $req)
    {
        $seller = $this->getCurrentSeller();
        $seller->forceFill(['api_token' => null])->save();

        return response()->json(['status' => 'revoked']);
    }
}