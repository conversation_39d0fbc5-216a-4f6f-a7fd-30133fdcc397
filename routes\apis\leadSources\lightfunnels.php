<?php

use App\Http\Controllers\LeadSources\LightfImportsController;
use Illuminate\Support\Facades\Route;

Route::prefix('lightf')->name('lightf.')->group(function () {

    Route::post('/addSourceLead', [LightfImportsController::class, 'addLightfSourceLead'])->middleware('auth.token');
    Route::get('/getStores/{token}', [LightfImportsController::class, 'getStores'])->middleware('auth.token');
    Route::get('/auth/save_state', [LightfImportsController::class, 'saveState']);
    Route::get('/auth/install', [LightfImportsController::class, 'install'])->middleware('auth.token');//->middleware('auth.token');
    Route::get('/auth/callback', [LightfImportsController::class, 'callback']);

    // Separate webhook handlers
    Route::post('/orders/create/{id}', [LightfImportsController::class, 'handleOrderCreated'])->name('order.created');
    Route::post('/app/uninstalled/{id}', [LightfImportsController::class, 'handleAppUninstalled'])->name('app.uninstalled');
});