<?php
namespace App\Services\Orders;

use App\Models\SellersColis;
use App\Models\SellersStock;
use App\Services\Fields\FieldsService;
use App\Services\Fields\FormatterOrders;
use App\Services\Orders\OrderStatusService;
use App\Services\Warehousing\FilterProducts;
use App\Traits\FilterQuery;
use Illuminate\Support\Facades\DB;
use SellersHelper;

class FilterOrders {
    protected $data;
    protected $query;
    protected $baseTable;
    protected $FieldsService;
    protected $FormatterOrders;
    protected $OrderStatusService;
    protected $currentSeller;
    protected $columnsType;
    use FilterQuery;

    public function __construct() {
        // Dependency Injection for better testability
        $this->FieldsService = new FieldsService();
        $this->FormatterOrders = new FormatterOrders();
        $this->OrderStatusService = new OrderStatusService();

        // Current Seller
        $this->currentSeller = SellersHelper::CurrentSeller();

        // Table name
        $this->baseTable = 'sellers_colis';
        $this->columnsType = 'orders';
    }

    /**
     * Get filtered orders based on input data.
     */
    public function getOrders(array $data) {
        // Store input data in the class property
        $this->data = $data;

        // Is Grouped
        $isGrouped =  (($this->data['groupStatus'] ?? null) || ($this->data['groupStatusPrices'] ?? null)) ? true : false;
        
        // Initialize query with default filtering conditions
        $this->initializeQuery();

        // General Filter
        $this->applyGeneralFilters();

        // keyword Filter
        $this->applyKeywordFilters();

        // produtcs Filter
        $this->applyProductFilters();

        // consignee Filter
        $this->applyConsigneeFilters();

        // shipping Filter
        $this->applyShippingFilters();

        // CallCenter Filter
        $this->applyCallCenterFilters();

         // Folowup Filter
         $this->applyFollowupFilters();

        // Dates Filter
        $this->applyDatesFilters();

        // Apply custom column selection based on input or defaults
        $this->applyCustomColumns();

        // Apply ordering based on user-specified or default parameters
        if(!$isGrouped){ $this->applyOrdering(); }

        // Return Grouped
        if($isGrouped){ return $this->fetchGrouped(); }

        // Fetch and return the final filtered results
        return $this->fetchResults($this->FormatterOrders);
    }

    /**
     * Initialize the base query with a default condition.
     */
    private function initializeQuery(): void {
        // Initial Query
        $this->query = SellersColis::where("{$this->baseTable}.statut_validate", 'open')->where("{$this->baseTable}.seller_id", $this->currentSeller->id);
        $this->query->where(function ($query) {
            $query->whereNotIn('order_type', ['firstmile'])
                ->orWhereNull('order_type')
                ->orWhere('order_type', '');
        });
    }

   /**
     * Apply general filters to the query.
     */
    private function applyGeneralFilters(): void{
        // Get the parent filter value from data, default to null if not set
        $id = $this->data["id"] ?? null;
        $idOrderCode = $this->data["idOrderCode"] ?? null;

        // Filter By id
        if($id){ $this->query->where("{$this->baseTable}.id", $id); }

        // Filter by list of IDs
        $ids = $this->data["ids"] ?? null;
        if ($ids && is_array($ids)) {
            $this->query->whereIn("{$this->baseTable}.id", $ids);
        }
        // By ID order Order Code
        if ($idOrderCode) {
            $this->query->where(function ($query) use ($idOrderCode) {
                $query->where("{$this->baseTable}.id", $idOrderCode)
                    ->orWhere("{$this->baseTable}.barecode", $idOrderCode);
            });
        }
    }

    /*
        Keyword Conditions
    */
    private function applyKeywordFilters(){
        $keyword = $this->data["keyword"] ?? NULL;

        // By keyword
        if($keyword && trim($keyword)){
            $this->query->where(function($q) use ($keyword){
                $q->where("{$this->baseTable}.barecode",'like','%'.$keyword.'%')
                ->orWhere("{$this->baseTable}.order_num",'like','%'.$keyword.'%')
                ->orWhere("{$this->baseTable}.tracking_number",'like','%'.$keyword.'%')
                ->orWhere("{$this->baseTable}.first_tracking_number",'like','%'.$keyword.'%')
                ->orWhere("{$this->baseTable}.recipient_phone",'like', '%'.$keyword.'%')
                ->orWhere("{$this->baseTable}.recipient_phone",'like',"%+".$keyword.'%')
                ->orWhere("{$this->baseTable}.seconde_recipient_phone",'like','%'.$keyword.'%')
                ->orWhere("{$this->baseTable}.seconde_recipient_phone",'like',"%+".$keyword.'%')
                ->orWhere("{$this->baseTable}.home_phone",'like','%'.$keyword.'%')
                ->orWhere("{$this->baseTable}.home_phone",'like',"%+".$keyword.'%')
                ->orWhere("{$this->baseTable}.recipient_phone", $keyword)
                ->orWhere("{$this->baseTable}.recipient_phone","+".$keyword)
                ->orWhere("{$this->baseTable}.seconde_recipient_phone",$keyword)
                ->orWhere("{$this->baseTable}.seconde_recipient_phone","+".$keyword)
                ->orWhere("{$this->baseTable}.recipient_name",'like','%'.$keyword.'%');
            });
        }
    }

    /**
     * apply Consignee Filters
     */
    private function applyConsigneeFilters(){
        // Get params
        $consigneeContact = $this->data["consigneeContact"] ?? NULL;
        $consigneePhone = $this->data["consigneePhone"] ?? NULL;

        // By consignee Contact
        if (!empty($consigneeContact)) {
            $this->query->where("{$this->baseTable}.recipient_name", 'like', "%{$consigneeContact}%");
        }

        // By consignee Phone Number
        if (!empty($consigneePhone)) {
            $this->query->where(function ($q) use ($consigneePhone) {
                foreach (['recipient_phone', 'seconde_recipient_phone', 'home_phone'] as $column) {
                    $q->orWhere("{$this->baseTable}.$column", 'like', "%{$consigneePhone}%")
                    ->orWhere("{$this->baseTable}.$column", 'like', "%+{$consigneePhone}%");
                }
            });
        }
    }

    /**
     * apply Shipping Filters
     */
    private function applyShippingFilters(){
        // Get params
        $originCountry = $this->data["originCountry"] ?? NULL;
        $destinationCountry = $this->data["destinationCountry"] ?? NULL;

        // origin Country
        if($originCountry){ $this->query->where("from_city_id",$originCountry); }

        // destination Country
        if($destinationCountry){ $this->query->where("city_livraison",$destinationCountry); }
    }

    /**
     * apply Followup Filters
     */
    private function applyFollowupFilters(){
        $followup = $this->data["followup"] ?? NULL;
        $listeFollowupStatus = $this->data["listeFollowupStatus"] ?? NULL;
        $excludedFollowupStatus = $this->data["excludedFollowupStatus"] ?? NULL;
        $followupStatus = $this->data["followupStatus"] ?? NULL;

        // is Followup
        if($followup == 1){
            $this->query->where(function($q){
                $q->where('order_type', "followup");
                $q->orWhere('motif_statut', 'Followup');
                $q->orWhere('is_follow_up','yes');
                $q->orWhere(function($q1){
                    $q1->where('feedback_updated',"!=","");
                    $q1->WhereNotNull('feedback_updated');
                });
            });
        }

        // Liste Followup Status
        if($listeFollowupStatus){
            if(in_array("new",$listeFollowupStatus)){
                $this->query->where(function($q) use ($listeFollowupStatus){
                    $q->whereIn('feedbacktype',$listeFollowupStatus)
                      ->orWhereNull('feedback_updated')
                      ->orWhere('feedback_updated','')
                      ->orWhereNull('feedbacktype')
                      ->orWhere('feedbacktype','');
                });
            }else{
                $this->query->whereIn('feedbacktype',$listeFollowupStatus);
            }
        }

        // Exlude Followup Status
        if($excludedFollowupStatus){ $this->query->whereNotIn('feedbacktype',$excludedFollowupStatus);   }

        // Followup Status
        if($followupStatus){ $this->query->where('feedbacktype',$followupStatus);   }
    }

    /**
     * apply Call Center Filters
     */
    private function applyCallCenterFilters(){
        // Get params
        $status = $this->data["status"] ?? NULL;
        $excludedStatus = $this->data["excludedStatus"] ?? NULL;
        $listStatus = $this->data["listStatus"] ?? NULL;
        $confirmed = $this->data["confirmed"] ?? NULL;
        $upsell = $this->data["upsell"] ?? NULL;
        $paymentMethod = $this->data["paymentMethod"] ?? NULL;
        $excludedOrderTypes = $this->data["excludedOrderTypes"] ?? NULL;

        // Exclude specific order types
        if ($excludedOrderTypes) {
            $this->query->where(function($q) use ($excludedOrderTypes) {
                $q->whereNotIn('order_type', $excludedOrderTypes)
                ->orWhereNull('order_type')
                ->orWhere('order_type', '');
            });
        }

        // Confirmed Orders
        if($confirmed == "yes"){ $this->query->where('is_validate',"yes"); }

        // have Upsell
        if($upsell == "yes"){ $this->query->where('upsell',"yes")->where('is_validate',"yes");}

        // No upsell
        if($upsell == "no"){
            $this->query->where('is_validate',"yes")->where(function($q) {
                $q->where('upsell', "")
                ->orWhereNull('upsell')
                ->orWhere('upsell', '!=','yes');
            });
        }

        // By Status
        if($status){
            if($status == "confirmed"){
                $this->query->where("is_validate","yes");
            }else{
                $this->query->where("statut_colis",$this->OrderStatusService->mapStandardStatuses(['status' => $status]));
            }

        }

        // excluded Status
        if($excludedStatus){
            $this->query->whereNotIn("statut_colis", $this->OrderStatusService->mapListStatuses(['listStatus' => $excludedStatus]));
        }

        // excluded Status
        if($listStatus){
            $this->query->whereIn("statut_colis", $this->OrderStatusService->mapListStatuses(['listStatus' => $listStatus]));
        }

        // By Payment Method
        if($paymentMethod){
            $this->query->where("payment_method",$paymentMethod);
        }
    }

    /**
     * Conditions Dates
     */
    private function applyDatesFilters(){
        // Get Params
        $startDate = $this->data['startDate'] ?? null;
        $endDate = $this->data['endDate'] ?? null;
        $dateType = $this->data['dateType'] ?? null;
        $status = $this->data['status'] ?? null;
        $followup = $this->data["followup"] ?? NULL;
        $year = $this->data["year"] ?? NULL;

        // By Year
        if ($year) {
            $this->query->whereYear("created_at",$year);
        }
        
        // Return early if no date filters are provided
        if ($startDate && $endDate) {
            // Determine the field name based on date type and status
            $fieldName = match ($dateType) {
                "leadDate" => "created_at",
                "shipDate" => "shipped_at",
                "followup" => "feedback_updated",
                "statusDate" => match ($status) {
                    "newlead" => "created_at",
                    "confirmed" => "validated_at",
                    "processing" => "transit_at",
                    "intransit" => "shipped_at",
                    "delivered" => "delivered_at",
                    "return" => "undelivered_at",
                    default => "date_motif_statut",
                },
                default => "created_at",
            };

           // Adjust field name if follow-up is enabled
            if ($fieldName == "created_at" && $followup == 1) {
                $fieldName = "feedback_created";
            }

            // Apply date filters
            if ($startDate) {
                $this->query->whereDate("{$this->baseTable}.{$fieldName}", ">=", $startDate);
            }
            if ($endDate) {
                $this->query->whereDate("{$this->baseTable}.{$fieldName}", "<=", $endDate);
            }
        }
    }


    /**
     * apply Product Filters
     */
    private function applyProductFilters(){
        $productReference = $this->data["productReference"] ?? null;

        // Exit early if no product reference is provided
        if ($productReference) {
            /*// Exit if the product is not found in stock
            $rowStock = SellersStock::find($productReference);

            // Retrieve all variant products if the product is variable
            $variantIds = [];
            if (($rowStock->id ?? null) && $rowStock->product_type === "variable") {
                $variantIds = (new FilterProducts())->getProducts([
                    'layout' => 'all',
                    'parent' => $rowStock->id,
                ]);
            }
            // Apply filtering based on the product reference and its variants
            $this->query->where(function ($q) use ($productReference, $variantIds) {
                $q->where("{$this->baseTable}.product_ids", 'like', "%;{$productReference};%");

                if (!empty($variantIds)) {
                    foreach ($variantIds['items'] as $variant) {
                        // Include each variant's ID in the filter
                        $q->orWhere("{$this->baseTable}.product_ids", 'like', "%;{$variant['id']};%");
                    }
                }
            });*/

            $this->query->where(function ($q) use ($productReference) {
                $q->where('product_ids', 'like', '%;' . $productReference . ';%')
                ->orWhereHas('sellers_colis_products', function ($sub) use ($productReference) {
                    $sub->where('main_product_id', $productReference)
                        ->orWhere('product_id', $productReference);
                });
            });

        }
    }

    /**
     * Fetch Result grouped
     */
    private function fetchGrouped(){
        // Grouped Field 
        $groupedField = $this->data['groupedField'] ?? "statut_colis";
        
        // Grouped By Status
        if($this->data['groupStatus'] ?? null){
            return $this->query->select($groupedField, DB::raw('COUNT(*) as total'))
                ->groupBy($groupedField)
                ->pluck('total', $groupedField);
        }

        // Grouped By Status and Price
        if($this->data['groupStatusPrices'] ?? null){
            return $this->query->select(
                    'statut_colis',
                    DB::raw('COUNT(*) as total'),
                    DB::raw('SUM(price) as total_price')
                )
                ->groupBy('statut_colis')
                ->get()
                ->keyBy('statut_colis')
                ->map(function ($item) {
                    return [
                        'total' => (int) $item->total,
                        'totalPrice' => (float) $item->total_price,
                    ];
                })
                ->toArray();
        }
      
    }
}