<?php
use App\Http\Controllers\LeadSources\WooCommerceImportsController;
use Illuminate\Support\Facades\Route;


Route::prefix('woocommerce')->name('woocommerce.')->group(function () {

    Route::post('/auth/install', [WooCommerceImportsController::class, 'install'])->middleware('auth.token')->name('woocommerce.install');

    Route::post('/orders/create/{id}', [WooCommerceImportsController::class, 'handleOrderWebhook'])->name('webhook');
    Route::post('/orders/toggleStatus/{sellerId}', [WooCommerceImportsController::class, 'toggleStatus'])->name('toggleStatus');
});


// [
//     'newlead','pending','schedule' : 'on hold',
//     'moanswer' : expired ? 'failed' : 'on hold',
//     'cancelled' : 'cncelled',
//     'duplicate','wrongphonenumber','test' : 'failed',
//     'confirmed' : 'on hold',
//     'delivered' :'completed',
//     'return' : 'refunded',
//     'intransit', 'processing' : 'processing'
// ]