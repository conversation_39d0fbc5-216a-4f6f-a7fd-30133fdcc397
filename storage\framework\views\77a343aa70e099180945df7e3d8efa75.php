
<?php $__env->startComponent('mail::message'); ?>
# Password Reset Request

Hello <?php echo e($name); ?>,

We received a request to reset your password.
Please use the following One-Time Password (OTP) to verify your identity:

<?php
    // Split the OTP code string into individual characters
    $digits = str_split($code);
?>
<table role="presentation" style="border-collapse: collapse; margin: 20px auto;">
    <tr>
        <?php $__currentLoopData = $digits; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $digit): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <td style="
                padding: 10px 15px;
                font-size: 28px;
                font-weight: bold;
                text-align: center;
                width: 40px;
                height: 50px;
                border-radius: 6px;
                margin-right: 5px;
                font-family: monospace, monospace;
                ">
                <?php echo e($digit); ?>

            </td>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </tr>
</table>

This OTP is valid for the next 60 minutes. If you did not request a password reset, please ignore this email.

Thanks,<br>
<?php echo e(config('app.name')); ?> Team
<?php echo $__env->renderComponent(); ?><?php /**PATH C:\Users\<USER>\Coding\COD\PowerGroupeSellersAPI\resources\views\emails\reset_code_plain.blade.php ENDPATH**/ ?>