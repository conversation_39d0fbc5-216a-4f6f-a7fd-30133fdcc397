<?php

namespace App\Http\Controllers\General;

use App\Http\Controllers\Controller;
use App\Models\SellersLivraison;
use App\Services\Shipping\FilterCountries;
use App\Services\Shipping\FilterCurrencies;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Se<PERSON>Helper;

class ShippingOptionsController extends Controller{
    protected FilterCountries $FilterCountries;
    protected FilterCurrencies $FilterCurrencies;

    /**
     * Inject the FilterCountries service into the controller
     */
    public function __construct(){
        $this->FilterCountries = new FilterCountries();
        $this->FilterCurrencies = new FilterCurrencies();
    }

    /**
     * List of countries
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function countries(Request $request): JsonResponse{
        // Initial Params
        $data = $request->all();

        // Check Type
        if($request->type){ $data['listeIds'] = $this->getCountryListByType($request->type); }


        // Retrieve the list of countries based on the provided filters
        $result = $this->FilterCountries->getCountries($data);
         // Prepare the base response
         $results = [
            'response' => 'success',
            'result' => $request->id
                ? $result
                : ($request->noPaginate
                ? ($this->formatterCountries(json_decode(json_encode($result), true) ?? []))

                    : ($this->formatterCountries($result['items']) ?? [])
                ),
        ];

        if (!$request->id && !$request->noPaginate) {
            $results['paginate'] = [
                'count' => $result['count'] ?? 0,
                'currentPage' => $result['currentPage'] ?? 1,
                'totalPages' => $result['totalPages'] ?? 1,
            ];
        }
        return response()->json($results);
        // Return the response as a JSON object

    }


    /**
     * Get unique country IDs based on type (origin or destination)
     */
    private function getCountryListByType(string $type): array{
        // Currrent Seller
        $seller = SellersHelper::CurrentSeller();

        // Validate type and determine the column to pluck
        if (!in_array($type, ['origin', 'destination'])) {
            return [];
        }

        $column = $type === 'origin' ? 'from_country_id' : 'country_id';

        // Query distinct country IDs where COD pricing is set
        return SellersLivraison::where('seller_id', $seller->id)
            ->where(function ($query) {
               $query->where('lastmile_pricecod', '>', 0)
                ->orWhere('lastmile_fixedcod', '>', 0)
                ->orWhere('firstmile_price', '>', 0)
                ->orWhereIn('invoiced',  ['non','no']);
            })
            ->pluck($column)
            ->unique()
            ->values()
            ->all();
    }

    /**
     * formatter Countries
     */
    private function formatterCountries($items){
        $newItems=[];
        foreach($items as $item){
            $flag = $item['code'];

            switch ($flag) {
                case 'KSA':
                    $flag = 'SA';
                    break;
                case 'UAE':
                    $flag = 'AE';
                    break;
                case 'IRQ':
                    $flag = 'IQ';
                    break;
                case 'PHL':
                    $flag = 'PH';
                    break;
                case 'MYS':
                    $flag = 'MY';
                    break;

                default:

                    break;
            }
            $newItems[] = [
                'id' => $item['id'],
                'name' => $item['name'],
                'code' => $item['code'],
                'flag' => $flag,
            ];
        }
        return $newItems;
    }
    /**
     * List of currencies
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function currencies(Request $request): JsonResponse{
        // Retrieve the list of countries based on the provided filters
        $result = $this->FilterCurrencies->getCurrencies($request->all());

        // Return the response as a JSON object
        return response()->json([
            'response' => 'success',
            'result' => $result['items'] ?? [],
            'paginate' => [
                'count' => $result['count'] ?? 0,
                'currentPage' => $result['currentPage'] ?? 1,
                'totalPages' => $result['totalPages'] ?? 1,
            ],
        ]);
    }
}