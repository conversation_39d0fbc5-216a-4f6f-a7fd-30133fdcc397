<?php

namespace App\Http\Controllers\LeadSources;

use App\Http\Controllers\Controller;
use App\Models\Sellers;
use App\Models\SellersApiImports;
use App\Services\LeadSource\LeadSourceService;
use App\Services\LeadSource\YouCanService;
use App\Services\Orders\OrderAPIService;
use GlobalHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use SellersHelper as GlobalSellersHelper;

class YoucanImportsController extends Controller
{
    protected $youcanService;
    protected $currentSeller;
    protected $leadSourceService;
    protected $OrderAPIService;

    public function __construct()
    {
        $this->currentSeller = GlobalSellersHelper::CurrentSeller();
        $this->youcanService = new YouCanService();
        $this->leadSourceService = new LeadSourceService();
        $this->OrderAPIService = new OrderAPIService();
    }

    /**
     * Install YouCan App - Redirect to authorization page.
     */
    public function install(Request $request){
        $redirectUri = env('YOUCAN_REDIRECT_URI');
        $scopes = [
            "read-orders",
            "edit-orders",
            "view-store-info",
            "edit-rest-hooks"
        ];
        $state = encrypt($this->currentSeller->id);

        $installUrl = "https://seller-area.youcan.shop/admin/oauth/authorize?"
            . "client_id=" . env('YOUCAN_API_KEY')
            . "&redirect_uri=" . urlencode($redirectUri)
            . "&response_type=code"
            . "&scope=" . implode('%20', array_map('urlencode', $scopes));

            return response()->json([
                'response' => 'success',
                'result' => $installUrl,
            ], 200);
    }


    /**
     * Handle OAuth Callback - Get Access Token and Register Webhooks
     */
    public function callback(Request $request)
    {
         // Validate request parameters
         $validationResponse = $this->youcanService->validateRequestParameters($request, ['code']);
         $data = $validationResponse->getData(true);

         if ($data['response'] === 'error') {
             return $validationResponse; // Return the error response if validation failed
         }
         $code = $data['result']['code'];

        $tokenService = $this->youcanService->getAccessToken($code);

        if ($tokenService->getData() === 'error') {
            return $tokenService; // Return the error response
        }

        $accessToken = $tokenService->getData()->result;
        Log::info('YouCan token', [
            'token' => $accessToken,
        ]);

        $baseUrl =env('URL_APP_SELLERS').'/lead-sources';

        $redirectUrl = $baseUrl . '?name=youcan&token=' . urlencode($accessToken);
          return redirect()->away($redirectUrl);
    }

    public function addLightfSourceLead(Request $request)
    {
        $request->validate([
            'token' => 'required|string',
        ]);


        // Get validated inputs
        $token = $request->input('token');

        // Register webhook
        $webhookUrl = route('youcan.webhook', ['id' => $this->currentSeller->id]); // Make sure you have this route defined

        $webhookResponse = $this->registerWebhookAndGetStore($token, $webhookUrl);

        if ($webhookResponse['response'] === 'error') {
            return response()->json($webhookResponse, 400);
        }

        $this->leadSourceService->createLeadSourceToken([
            'api_name' => 'youcan',
            'api_token' => $token,
            'seller' => $this->currentSeller,
            'webhook' => json_encode($webhookResponse['result']),
            'api_label' => $webhookResponse['store']['name'] ?? 'YouCan',
            'shopurl' => $webhookResponse['store']['domain'] ?? 'https://youcan.shop',
        ]);


        return response()->json([
            'response' => 'success',
            'message' => 'YouCan lead source added successfully',
        ], 200);
    }

    /**
     * Subscribe to order creation Webhook and fetch store info
     */
    private function registerWebhookAndGetStore($accessToken, $webhookUrl)
    {
        // Step 1: Register the webhook
        $webhookResponse = Http::withHeaders([
            'Authorization' => 'Bearer ' . $accessToken,
        ])->asForm()->post('https://api.youcan.shop/resthooks/subscribe', [
            'event'      => 'order.create',
            'target_url' => $webhookUrl,
        ]);

        // Step 2: Fetch store name and domain
        $storeInfo = $this->getStoreInfo($accessToken);

        // Final response
        if ($webhookResponse->ok()) {
            return [
                'response' => 'success',
                'result'   => $webhookResponse->json(),
                'store'    => $storeInfo,
            ];
        } else {
            return [
                'response' => 'error',
                'message'  => $webhookResponse->json(),
                'store'    => $storeInfo,
            ];
        }
    }

    /**
     * Get store info (name and domain) from YouCan API
     */
    private function getStoreInfo($accessToken): array
    {
        $response = Http::withHeaders([
            'Authorization' => 'Bearer ' . $accessToken,
            'Accept'        => 'application/json',
        ])->get('https://api.youcan.shop/me');

        if ($response->ok()) {
            $data = $response->json();
            return [
                'id' => $data['id'] ?? null,
                'name'   => $data['name'] ?? null,
                'domain' => $data['domain'] ?? null,
            ];
        }

        return [
            'id' => null,
            'name'   => null,
            'domain' => null,
        ];
    }


     /**
     * Handle Order Webhook
     */
    public function handleOrderWebhook(Request $request,$id)
    {

        $request_from_youcan = $this->youcanService->isValidYouCanSignature($request);

        if ($request_from_youcan !== true) {
            return $request_from_youcan; // HACKER possibility here
        }

        $storeName = $this->getLightfSourceLead($id, $request->input('store_id'));

        if (!$storeName) {
            return response()->json([
                'response' => 'error',
                'message' => 'Not our store',
            ], 400);
        }
        // Formatter Data
        $orderData = $this->youcanService->formatWebHookOrder($request->all(), $storeName
        );

        $responseOrders = $this->setLeads($orderData, $id);
        if ($responseOrders['response'] === 'error') {
            return response()->json($responseOrders, 400);
        }


        // Return Response
        return response()->json($responseOrders, 200 );
    }


    private function getLightfSourceLead($sellerId,$storeId)
    {
        $response = SellersApiImports::where('seller_id', $sellerId)
            ->where('api_name', 'youcan')
            ->first();

        $storeInfo = $this->getStoreInfo($response->api_token);
        if($storeInfo['id'] === $storeId && $storeInfo['name'] === $response->api_label){
            return $storeInfo['name'];
        }

        return null;


    }

    /**
     *
     *
     * @param mixed $orderData
     * @param mixed $id
     * @return array{response: string, result: array|array{message: string, response: string|array{response: string}}}
     */
    function setLeads( $orderData, $id)
    {
        $responseOrders = $this->OrderAPIService->sendOrders([
            'orderData' => $orderData,
            'sellerId' => $id
        ]);

        // Check if the response contains an error
        if ($responseOrders['response'] === 'error') {
            Log::error('Order API Service returned an error', [
                'seller_id' => $id,
                'order_data' => $orderData,
                'response' => $responseOrders
            ]);
            return [
                'response' => 'error',
                'result' => $responseOrders,
            ];
        }
        //set lastImport date
        SellersApiImports::where('seller_id', $id)
            ->where('api_name', 'youcan')
            ->update([
                'last_imported_order' => count($orderData),
                'last_imported_date' => now(),
            ]);
            return [
                'response' => 'success',
                'result' => $responseOrders,
            ];
    }


    /**
     * Retrieves a list of orders from YouCan.
     *
     * @param Request $request The incoming request instance.
     * @return \Illuminate\Http\JsonResponse The JSON response containing the list of orders or an error message.
     */
    public function getOrders(Request $request)
    {
        // Retrieve YouCan API token (replace with your logic)
        $accessToken = $this->youcanService->checkYouCanToken();


         // Validate and collect base query parameters
         $queryParams = $request->only(['include', 'page', 'limit']);

         // Handling filters dynamically
         if ($request->has('filters')) {
             $filters = $request->input('filters');
             foreach ($filters as $index => $filter) {
                 if (isset($filter['field'], $filter['value'])) {
                     $queryParams["filters[$index][field]"] = $filter['field'];
                     $queryParams["filters[$index][value]"] = $filter['value'];
                 }
             }
         }

        // Make API request
        $response = Http::withToken($accessToken)
            ->get('https://api.youcan.shop/orders', $queryParams);

        // Handle response
        if ($response->failed()) {
            return response()->json([
                'response' => 'error',
                'message' => __('sourcing.failed_to_fetch_orders_from_youcan'),
            ], $response->status());
        }

        return response()->json([
            'response' => 'success',
            'result' => $response->json(),
        ], 200);
    }
}