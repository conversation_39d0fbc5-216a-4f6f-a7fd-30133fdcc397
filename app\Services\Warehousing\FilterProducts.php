<?php
namespace App\Services\Warehousing;

use App\Models\SellersStock;
use App\Services\Fields\FieldsService;
use App\Services\Fields\FormatterProducts;
use App\Traits\FilterQuery;
use Illuminate\Support\Facades\DB;
use SellersHelper;

class FilterProducts {
    protected $data;
    protected $query;
    protected $baseTable;
    protected $FieldsService;
    protected $FormatterProducts;
    protected $currentSeller;
    protected $columnsType;
    use FilterQuery;

    public function __construct() {
        // Dependency Injection for better testability
        $this->FieldsService = new FieldsService();
        $this->FormatterProducts = new FormatterProducts();

        // Current Seller
        $this->currentSeller = SellersHelper::CurrentSeller();

        // Table name
        $this->baseTable = 'sellers_stock';
        $this->columnsType = 'products';
    }

    /**
     * Get filtered orders based on input data.
     */
    public function getProducts(array $data) {
        // Store input data in the class property
        $this->data = $data;

        // Initialize query with default filtering conditions
        $this->initializeQuery();

        // General Conditions
        $this->applyGeneralFilters();

        // keyword Filter
        $this->applyKeywordFilters();

        // Apply custom column selection based on input or defaults
        $this->applyCustomColumns();

        //add remaining quantity
        $this->GetRmainingQuantity();

        // Apply ordering based on user-specified or default parameters
        $this->applyOrdering();

        // Fetch and return the final filtered results
        return $this->fetchResults();
    }

    /**
     * Initialize the base query with a default condition.
     */
    private function initializeQuery(): void {
        // Initial Query
        $this->query = SellersStock::where("{$this->baseTable}.seller_id", $this->currentSeller->id);

        // Check Detail Layout
        if(($this->data['with'] ?? null)){
            $this->query->with($this->data['with']);
        }
    }

    /**
     * Apply custom column selection if provided, otherwise use default columns.
     */
    private function applyCustomColumns(): void {
        $customColumns = $this->data['custom_columns'] ?? [];

        if (!empty($customColumns)) {
            $this->query->select($customColumns);
        } else {
            $this->data = $this->FieldsService->DefaultColumns($this->columnsType, $this->data);
            $this->query->select($this->data['columns']);
        }
    }

      /*
        Keyword Conditions
    */
    private function applyKeywordFilters(){
        $keyword = $this->data["keyword"] ?? NULL;

        // By keyword
        if($keyword && trim($keyword)){
            $this->query->where(function($q) use ($keyword){
                $q->where("{$this->baseTable}.name",'like','%'.$keyword.'%')
                ->orWhere("{$this->baseTable}.arabic_name",'like','%'.$keyword.'%')
                ->orWhere("{$this->baseTable}.reference",'like','%'.$keyword.'%');
            });
        }
    }

    /**
     * Get remaining quantity for each product.
     */
    private function GetRmainingQuantity(){

        $this->query->addSelect("{$this->baseTable}.remaining_quantity as remainingQuantity");
    }
   /**
     * Apply general filters to the query.
     */
    private function applyGeneralFilters(){
        // Get the parent filter value from data, default to null if not set
        $parent = $this->data["parent"] ?? null;
        $id = $this->data["id"] ?? null;
        $archive = $this->data["archive"] ?? null;
        $productType = $this->data["productType"] ?? null;
        $category = $this->data["category"] ?? null;
        $sku = $this->data["sku"] ?? null;

        // Filter By id
        if($id){ $this->query->where("{$this->baseTable}.id", $id); }

        // Archive Products
        if ($archive == 1) {
            $this->query->where('is_archive', 1);
        } else {
            $this->query->where(function ($q) {
                $q->where('is_archive', 0)
                    ->orWhere('is_archive', "")
                    ->orWhereNull('is_archive');
            });
        }

        // Filter By SKU
        if ($sku) {
            $this->query->where("{$this->baseTable}.reference", $sku);
        }

        // Filter By Product Type
        if($productType){
            $this->query->where("{$this->baseTable}.product_type", $productType);
        }

        // Filter By Category
        if($category){
            $this->query->where("{$this->baseTable}.categorie_name", $category);
        }

        // Filter By Parent
        if($parent){
            $this->query->where(function ($query) use ($parent) {
                // No parent
                if ($parent === "noparent") {
                    $query->whereNull("{$this->baseTable}.parent")
                        ->orWhere("{$this->baseTable}.parent", '')
                        ->orWhere("{$this->baseTable}.parent", 0);
                }
                // Has a parent
                elseif ($parent === "haveparent") {
                    $query->whereNotNull("{$this->baseTable}.parent")
                        ->where("{$this->baseTable}.parent", '!=', '')
                        ->where("{$this->baseTable}.parent", '!=', 0);
                }
                // Specific parent ID
                else {
                    $query->where("{$this->baseTable}.parent", $parent);
                }
            });
        }
    }

    /**
     * Apply ordering to the query based on provided parameters.
     */
    private function applyOrdering(): void {
        $orderBy = $this->data['orderby'] ?? "{$this->baseTable}.id";
        $orderDirection = $this->data['ordering'] ?? 'DESC';

        $this->query->orderBy($orderBy, $orderDirection);
    }

    /**
     * Fetch results based on pagination and other parameters.
     */
    private function fetchResults() {
        $id = $this->data['id'] ?? null;
        $count = $this->data['count'] ?? false;
        $perPage = $this->data['perPage'] ?? env('PER_PAGE', 15);
        $noPaginate = $this->data['noPaginate'] ?? false;
        $toArray = $this->data['toArray'] ?? false;
        $first = $this->data['first'] ?? false;
        $noFormatter = $this->data['noFormatter'] ?? false;

        // If count is requested, return the total count of matching records
        if ($count) {
            return $this->query->count();
        } elseif ($toArray) {
            $results = $this->query->get()->toArray();

        } elseif ($id) {
            $results = $this->query->first();
            $first = true;
            $noPaginate = true;

        } else {
            // Otherwise, fetch results with or without pagination
            $results = $noPaginate ? $this->query->get() : $this->query->paginate($perPage);
        }


        // Return results, optionally formatting them using FieldsHelper
        return $noFormatter ? $results : $this->FieldsService->ResponseResults([
            'initialResults' => $results,
            'results' => $this->FormatterProducts->formatterResult([
                "items" => $first == 1 ? [$results] : ($noPaginate ? $results : $results->items()),
                'first' => $first,
            ]),
            'first' => $first,
            'noPaginate' => $noPaginate,
            'layout' => $this->data['layout'] ?? null,
            'type' => $this->columnsType,
            'resultFormatted' => 1,
        ]);
    }
}