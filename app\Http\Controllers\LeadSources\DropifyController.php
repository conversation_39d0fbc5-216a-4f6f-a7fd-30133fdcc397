<?php

namespace App\Http\Controllers\LeadSources;

use App\Http\Controllers\Controller;
use App\Models\SellersMetadata;
use App\Services\LeadSource\DropifyService;
use App\Services\LeadSource\LeadSourceService;
use App\Services\Orders\OrderAPIService;
use GlobalHelper;
use Illuminate\Http\Request;
use SellersHelper as GlobalSellersHelper;
class DropifyController extends Controller
{
    protected $dropifyService;
    protected $currentSeller;
    protected $leadSourceService;
    protected $OrderAPIService;

    public function __construct(){
        $this->currentSeller = GlobalSellersHelper::CurrentSeller();
        $this->dropifyService = new DropifyService();
        $this->leadSourceService = new LeadSourceService();
        $this->OrderAPIService = new OrderAPIService();
    }

    /**
     * Validate if the request parameters are from Dropify.
     */
    public function isDropifyRequest(Request $request){
        $validationResponse = $this->dropifyService->validateRequestParameters($request, ['client_id','redirect_uri']);
        $data = $validationResponse->getData(true);

        if ($data['response'] === 'error') {
            return $validationResponse; // Return the error response if validation failed
        }
        $isValid = $this->dropifyService->isValidDropifySignature($request->all());

        if ($isValid !== true) {
            return $isValid;
        }

        return response()->json([
            'response' => 'success',
        ],200);
    }


    /**
     * Handle OAuth Callback - Get Access Token and Redirect.
     */
    public function install(Request $request)
    {
        $validationResponse = $this->dropifyService->validateRequestParameters($request, ['client_id','redirect_uri']);
        $data = $validationResponse->getData(true);

        if ($data['response'] === 'error') {
            return $validationResponse; // Return the error response if validation failed
        }
        $request_from_dropify = $this->dropifyService->isValidDropifySignature($request->all(),true);

        if ($request_from_dropify !== true) {
            return $request_from_dropify; // HACKER possibility here
        }

        $token = $this->CheckToken();

        $this->leadSourceService->createLeadSourceToken([
            'api_name' => 'dropify',
            'api_token' => $token,
            'seller' =>  $this->currentSeller,
            'api_label' => 'Dropify',
            'shopurl' => 'Dropify',
        ]);


        $redirect_uri = base64_decode($data['result']['redirect_uri']);

         return response()->json([
            'response' => 'success',
            'result' => [
                'redirect_uri' => $redirect_uri . '?token=' . $token]

        ],200);

    }
    /**
     * Handle OAuth Callback - Get Access Token and Redirect.
     */
    public function setOrders(Request $request){
        // Retrieve seller_id that was added in the middleware
        $sellerId = $request->header('seller-id');

        // Get request data and ensure it's an array
        $dataCheck = $this->addIdentity($request);
        if ($dataCheck['response'] === 'error') {
            return response()->json($dataCheck, 400);
        }

        // Send Order via api
        $responseOrders = $this->OrderAPIService->sendOrders([
            'orderData' => $dataCheck['orders'],
            'sellerId' => $sellerId,
        ]);

        // Check if the response contains an error
        if ($responseOrders['response'] === 'error') {
            return response()->json($responseOrders, 400);
        }
        // Return Response
        return response()->json($responseOrders, 200 );
    }

    /**
     * add Identity Source
     */
    private function addIdentity($request){
        $requestData = $request->all();
        if (is_array($requestData)) {
            // Add orderSource to each item in the array
            $requestData = array_map(function($item) {
                if (is_array($item)) {
                    $item['orderSource'] = 'dropify';
                }
                return $item;
            }, $requestData);

            return [
                'response' => 'success',
                'orders' => $requestData,
            ];
        }else{
            return [
                'response' => 'error',
                'message' => 'Invalid request data format',
            ];
        }
    }

    /**
     * Check Client Token
     */
    private function CheckToken(){
        $sellersMetadata = SellersMetadata::where('seller_id', $this->currentSeller->id)
        ->where("meta_data", 'dropify_seller_token')->first();
        // check if already have a token
        if ($sellersMetadata && $sellersMetadata->meta_value) {
            return $sellersMetadata->meta_value;
        } else {
            //Create new token
            $token = GlobalHelper::generateToken();
            SellersMetadata::create([
                'seller_id' => $this->currentSeller->id,
                'meta_data' => 'dropify_seller_token',
                'meta_value' => $token,
            ]);
            return $token;
        }
    }
}