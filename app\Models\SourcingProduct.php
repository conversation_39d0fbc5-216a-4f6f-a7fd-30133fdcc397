<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;

class SourcingProduct extends Model
{
    protected $table = "seller_sourcing_product";

    public $timestamps = true;

	protected $fillable = [
	    'seller_id',
	    'seller_name',
        'sourcing_request_id',
        'sourcing_request_code',
	    'arabic_name',
	    'product_link',
	    'created_by',
	    'updated_by',
	    'categorie_name',
	    'is_test',
        'process_mode',
        'origin_country',
        'destination_country',
	    'quantity',
        'shipping_method',
        'note',

        'name',
        'agreed_price'
	];

    public function variants()
    {
        return $this->hasMany(SourcingProductVariant::class, 'product_id');
    }
}