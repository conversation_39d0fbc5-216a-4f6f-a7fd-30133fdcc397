<?php
use App\Models\Medias;
use Illuminate\Support\Str;
use Intervention\Image\ImageManagerStatic as Image;

class MediaHelper{
    /**
     * Get the full file path by size
     * @param  array $options
     * @return string
     */
    public static function getFileUrl(array $options): string {
        $fullPath = "";
        $media = $options['media'] ?? null;
        $sizeName = $options['sizeName'] ?? null;

        // Retrieve media if not already provided
        if (!$media) {
            $options['first'] = 1;
            $media = self::getMediaByTable($options);
        }

        if ($media) {
            $fileName = $media->media ?? "";
            if ($fileName) {
                $path = $media->table_name . "/";
                
                // Generate the full file path with size name if provided
                if ($sizeName) {
                    $fullPath = Self::getAssetUrl($path . "$sizeName-$fileName");
                } else {
                    $fullPath = Self::getAssetUrl($path . "$fileName");
                }

                // Check if the file exists and is an image
                if (!@getimagesize($fullPath)) {
                    $fullPath = Self::getAssetUrl($path . "$fileName");
                }
            }
        }
        return $fullPath;
    }

    /**
     * Get media list by table name and table id
     * @param  array $options
     * @return Object
     */
    public static function getMediaByTable(array $options) {
        $tableName = $options['tableName'];
        $tableId = $options['tableId'];
        $imageSlug = $options['imageSlug'] ?? null;
        $first = $options['first'] ?? null;

        // Query to get media list based on table name, table id, and optional image slug
        $query = Medias::where("table_name", $tableName)
                    ->where('table_id', $tableId);

        if ($imageSlug) {
            $query->where('image_slug', $imageSlug);
        }

        $mediaList = $query->orderBy('id', 'desc');
        if ($first) {
            $mediaList = $mediaList->first();
        } else {
            $mediaList = $mediaList->get();
        }

        return $mediaList;
    }
    
    /**
     * save Media
     */
    public static function saveMedia($options = array()){
        // Extract values from the options array
        $request = $options['request'] ?? null;
        $fileName = $options['fileName'];
        $imageSlug = $options['imageSlug'];
        $tableName = $options['tableName'];
        $tableId = $options['tableId'];
        $sourceUrl = $options['sourceUrl'] ?? null;


        $folder = $tableName . "/";
        // Check if there is a file to upload or if a source URL is provided
        if (($request && $request->hasFile($fileName)) || $sourceUrl) {

            // Delete old media files associated with the table
            self::deleteOldFiles($options);

            // Generate a new file name for the uploaded file
            $savefileName = self::generateFileName($request, $fileName, $tableName, $tableId, $sourceUrl);
            $filePath = env('UploadPath') . "/" . $folder;

            // Create the folder if it doesn't exist
            if (!is_dir($filePath)) {
                mkdir($filePath, 0777, true);
            }

            // Handle the file upload or image download from the URL
            if (self::handleFileUpload($request, $fileName, $filePath, $savefileName, $sourceUrl)) {

                // Save the media record in the database
                return Medias::create([
                    'table_name' => $tableName,
                    'table_id' => $tableId,
                    'image_slug' => $imageSlug,
                    'media' => $savefileName,
                ]);
            }
        }

        // Return false if no valid file or source URL was provided
        return false;
    }

    /**
     * delete Old Files
     */
    public static function deleteOldFiles($options){
        // Get the list of old media files associated with the table
        $listeImage = self::getMediaByTable($options);
        if ($listeImage) {
            // Define the file path for media storage
            $filePath = env('UploadPath') . "/" . $options['tableName'] . "/";

            // Iterate over each old media file and delete it
            foreach ($listeImage as $img) {
                // Delete the original media file
                @unlink($filePath . $img->media);

                // Delete resized versions of the media
                foreach (self::listeSize() as $type => $size) {
                    @unlink($filePath . $type . "-" . $img->media);
                }

                // Remove the media record from the database
                Medias::where('id', $img->id)->delete();
            }
        }
    }

    /**
     * generate File Name
     */
    private static function generateFileName($request, $fileName, $tableName, $tableId, $sourceUrl){
        // If the file is uploaded via the request, generate a unique file name
        if ($request) {
            $file = $request->file($fileName);
            $ext = $file->getClientOriginalExtension();  // Get file extension
            return Str::slug($fileName . '-' . $tableName . '-' . base64_encode($tableId) . '-' . uniqid()) . ".$ext";
        }
        // If the source URL is provided, generate a file name using the URL and table information
        elseif ($sourceUrl) {
            return $tableName . "-" . $tableId . "-" . uniqid() . "-" . basename($sourceUrl);
        }

        // Return an empty string if neither request nor source URL is present
        return '';
    }

    /**
     * handle File Upload
     */
    private static function handleFileUpload($request, $fileName, $filePath, $savefileName, $sourceUrl){
        // If the file is uploaded via the request, handle the file upload
        if ($request) {
            // Get the file from the request
            $file = $request->file($fileName);
            $ext = $file->getClientOriginalExtension();
            $allowedExtension = self::allowedExtension();

            // Check if the file's extension is allowed
            if (!in_array(strtolower($ext), $allowedExtension)) {
                return false;
            }

            // Move the file to the designated path with the new file name
            return $file->move($filePath, $savefileName);
        }elseif ($sourceUrl) {
            include public_path() . '/library/Image/vendor/autoload.php';
            Image::make($sourceUrl)->save($filePath . $savefileName);

        }

        // Return false if neither a file nor a source URL is provided
        return false;
    }

	/**
	 * Get liste size
	 */
	private static function listeSize(){
		$liste_size = array(
			'thumb'=>150,
			'small'=>50,
			'medium'=>300,
			'ico'=>30,
		);
		return $liste_size;
	}


    /**
     * Get the full asset URL by combining the ERP base URL with the given path.
     */
    public static function getAssetUrl($path){
        return $path ? rtrim(env('UPLOADS_BASE_URL'), '/') . '/' . ltrim($path, '/') : null;
    }

    /**
	 * Get liste allowed extension for save file
	 */
	public static function allowedExtension(){
		$liste = array("png","jpg","jpeg","xls","xlsx","pdf","gif");
		return $liste;
	}


}