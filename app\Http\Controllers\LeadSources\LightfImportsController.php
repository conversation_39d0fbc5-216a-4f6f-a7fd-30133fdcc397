<?php

namespace App\Http\Controllers\LeadSources;

use App\Http\Controllers\Controller;
use App\Models\Sellers;
use App\Models\SellersApiImports;
use App\Services\LeadSource\LeadSourceService;
use App\Services\LeadSource\LightfService;
use App\Services\Orders\OrderAPIService;
use Illuminate\Support\Facades\Session;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use SellersHelper as GlobalSellersHelper;

class LightfImportsController extends Controller
{
    protected $lightfService;
    protected $currentSeller;
    protected $leadSourceService;
    protected $OrderAPIService;

    public function __construct()
    {
        $this->currentSeller = GlobalSellersHelper::CurrentSeller();
        $this->lightfService = new LightfService();
        $this->leadSourceService = new LeadSourceService();
        $this->OrderAPIService = new OrderAPIService();
    }

    /**
     * Install LightFunnels App - Redirect to authorization page.
     */
    public function install(Request $request){
        // Generate a random state and store it in session for verification later


        $redirectUri = env('LIGHTF_REDIRECT_URI');
        $scopes = "orders,funnels,customers,products";
        $state = Str::random(40); // Generate a random state string
        Session::put('oauth_lightf_state', $state); // Store in session

        $installUrl = "https://app.lightfunnels.com/admin/oauth?"
            ."client_id=" . env('LIGHTF_API_KEY')
            ."&redirect_uri={$redirectUri}"
            ."&response_type=code"
            ."&scope={$scopes}"
            ."&state={$state}";


            return response()->json([
                'response' => 'success',
                'result' => $installUrl,
            ], 200);
    }

    /**
     * Save the state in session
     */
    public function saveState(Request $request){

        // Validate request parameters
        $validationResponse = $this->lightfService->validateRequestParameters($request, ['cod_state']);
        $data = $validationResponse->getData(true);

        if ($data['response'] === 'error') {
            return $validationResponse; // Return the error response if validation failed
        }
        // Remove the old session value if it exists and replace it with the new one
        Session::put('oauth_lightf_state' , $data['result']['cod_state']);

        return response()->json(['response'=>'success',
                         'message'=> __('sourcing.state_saved')], 200);
    }


    /**
     * Handle OAuth Callback - Get Access Token
     */
    public function callback(Request $request)
    {
        // Validate request parameters
        $validationResponse = $this->lightfService->validateRequestParameters($request, ['code','state']);
        $data = $validationResponse->getData(true);

        if ($data['response'] === 'error') {
            return $validationResponse; // Return the error response if validation failed
        }
        $code = $data['result']['code'];

        $tokenService = $this->lightfService->getAccessToken($code);

        if ($tokenService->getData()->response === 'error') {
            return $tokenService; // Return the error response
        }

        $accessToken = $tokenService->getData()->result; // Extract the token from the response


        $baseUrl =env('URL_APP_SELLERS').'/lead-sources';


        $redirectUrl = $baseUrl . '?name=lightfunnels&token=' . urlencode($accessToken);
        return redirect()->away($redirectUrl);

    }

    public function getStores(Request $request,$token)
    {
        $stores = $this->lightfService->getStores($token);

        if ($stores['response'] === 'error') {
            return response()->json([
                'response' => 'error',
                'message' => 'Failed to fetch stores',
            ], 400);
        }

        $result = [
            'fromLightf' => $stores['result'] ?? [],
        ];

        $installedStores = $this->getLightfSourceLead($this->currentSeller->id);
        if($installedStores && $installedStores->shopurl !== 'All'){
            $result['installed'] =  explode(', ', $installedStores->shopurl);
        }else{
            $result['installed'] = [];
        }

        return response()->json([
            'response' => 'success',
            'result' => $result,
        ], 200);
    }
    public function addLightfSourceLead(Request $request)
    {
        $request->validate([
            'token' => 'required|string',
            'stores' => 'required|string', // still string at this point (JSON-encoded)
        ]);

        $token = $request->input('token');
        // Decode the JSON string to array
        $selectedStores = json_decode($request->input('stores'), true);

        if (!is_array($selectedStores)) {
            return response()->json([
                'response' => 'error',
                'message' => 'Invalid stores data format',
            ], 422);
        }
        //check if not empty
        $api_label = 'LightFunnels (All)';
        $api_store_ids = 'All';
        if(!empty($selectedStores)){
            // Validate selected store IDs exist in Lightfunnels store list
            $validationResponse = $this->validateSelectedStores($token, $selectedStores);
            if ($validationResponse['response'] !== 'success') {
                return response()->json($validationResponse, 400); // Return the validation error response
            }
            $stores = $validationResponse['result'];


            //make api_label from stores
            $api_label = count($stores) === 1 ? $stores[0]['defaultDomain'] : implode(', ', array_column($stores, 'defaultDomain'));
            $api_store_ids = count($stores) === 1 ? $stores[0]['id'] : implode(', ', array_column($stores, 'id'));

        }
        $webhookEvents = [
            'order/created' => route('lightf.order.created', ['id' => $this->currentSeller->id]),
            'app/uninstalled' => route('lightf.app.uninstalled', ['id' => $this->currentSeller->id]),
            // Add more here later (like 'order/updated' => route(...))
        ];

        $webhooks = [];

        foreach ($webhookEvents as $eventType => $url) {
            $response = $this->registerWebhook($token, $url, $eventType);
            $webhooks[$eventType] = $response['result']['id'] ?? null;

        }
        $leadSource = $this->getLightfSourceLead($this->currentSeller->id);
        if ($leadSource) {
            $leadSource->update([
                'api_token' => $token,
                'webhook' => json_encode($webhooks),
                'api_label' => $api_label,
                'shopurl' => $api_store_ids,
            ]);
            return response()->json([
                'response' => 'success',
                'message' => 'LightFunnels lead source updated successfully',
            ], 200);
        }else{
            $this->leadSourceService->createLeadSourceToken([
                'api_name'   => 'lightFunnels',
                'api_token'  => $token,
                'seller'     => $this->currentSeller,
                'webhook'    => json_encode($webhooks),
                'api_label'  => $api_label,
                'shopurl'    => $api_store_ids,
            ]);
            return response()->json([
                'response' => 'success',
                'message' => 'LightFunnels lead source added successfully',
            ], 200);
        }


    }

    /**
     * Validate that all selected store IDs exist in the Lightfunnels store list.
     */
    private function validateSelectedStores(string $token, array $selectedStores)
    {
        $fetched = $this->lightfService->getStores($token);

        if ($fetched['response'] === 'error') {
            return [
                'response' => 'error',
                'message' => 'Failed to fetch stores from Lightfunnels',
            ];
        }

        $availableStores = $fetched['result'] ?? [];
        $availableStoreIds = array_column($availableStores, 'id');

        $invalidStores = array_filter($selectedStores, function ($storeId) use ($availableStoreIds) {
            return !in_array($storeId, $availableStoreIds);
        });

        if (!empty($invalidStores)) {
            return [
                'response' => 'error',
                'message' => 'Some selected stores are not valid',
            ];
        }
        // Filter and map valid selected store info
        $matchedStores = array_values(array_filter($availableStores, function ($store) use ($selectedStores) {
            return in_array($store['id'], $selectedStores);
        }));

        $enriched = array_map(function ($store) {
            return [
                'id'             => $store['id'],
                'defaultDomain'  => $store['defaultDomain'] ?? $store['slug']
            ];
        }, $matchedStores);

        return [
            'response' => 'success',
            'result' => $enriched,
        ];
    }


    /**
     * Register webhook with Lightfunnels
     */
    protected function registerWebhook($accessToken, $webhookUrl, $eventType)
    {
        $query = [
            'query' => '
                mutation CreateWebhookMutation($node: WebhookInput!) {
                    createWebhook(node: $node) {
                        id
                        type
                        url
                        settings
                    }
                }
            ',
            'variables' => [
                'node' => [
                    'type' => $eventType,
                    'url' => $webhookUrl,
                    'settings' => new \stdClass(),
                ]
            ]
        ];

        try {
            $client = new \GuzzleHttp\Client();
            $response = $client->post('https://services.lightfunnels.com/api/v2', [
                'headers' => [
                    'Authorization' => 'Bearer ' . $accessToken,
                    'Content-Type' => 'application/json',
                ],
                'body' => json_encode($query),
            ]);

            $data = json_decode($response->getBody(), true);

            if (!isset($data['data']['createWebhook'])) {
                return [
                    'response' => 'error',
                    'event' => $eventType,
                    'message' => 'Failed to register webhook',
                ];
            } else {
                return [
                    'response' => 'success',
                    'event' => $eventType,
                    'result' => $data['data']['createWebhook'],
                ];
            }
        } catch (\Exception $e) {
            return [
                'response' => 'error',
                'event' => $eventType,
                'message' => $e->getMessage(),
            ];
        }
    }



    public function handleOrderCreated(Request $request, $id)
    {
        // Verify webhook signature
        $verification = $this->verifyLightfWebhook($request);
        if ($verification !== true) {
            return $verification;
        }

        // Get seller API import config
        $sourceLead = $this->getLightfSourceLead($id);
        if (!$sourceLead) {
            return response()->json([
                'response' => 'error',
                'message' => 'Lead source not found',
            ], 404);
        }

        // Verify if the incoming order is from one of our stores
        $data = $request->all();
        if (!$this->isOrderFromOurStore($data, $sourceLead)) {
            Log::info('Not our store', ['request' => $data]);
            return response()->json([
                'response' => 'error',
                'message' => 'Not our store',
            ], 400);
        }

        // Get store name (slug or defaultDomain)
        $storeId = $data['node']['store_id'];
        $storeName = $this->getStoreName($storeId, $sourceLead->api_token);

        // Format order
        $orderData = $this->lightfService->formatWebHookOrder($data, $storeName);

        // Send to Order API Service
        $responseOrders = $this->OrderAPIService->sendOrders([
            'orderData' => $orderData,
            'sellerId' => $id,
        ]);

        if ($responseOrders['response'] === 'error') {
            Log::error('Order API Service returned an error', [
                'seller_id' => $id,
                'order_data' => $orderData,
                'response' => $responseOrders,
            ]);
            return response()->json($responseOrders, 400);
        }

        // Update import stats
        $this->updateImportStats($sourceLead, count($orderData));

        return response()->json($responseOrders, 200);
    }


    /**
     * Handle App Uninstalled Webhook
     */
    public function handleAppUninstalled(Request $request,$id)
    {


        $request_from_lightf = $this->lightfService->verifyWebhook($request);
        // save it same why as excel order
        if ($request_from_lightf !== true) {

            return $request_from_lightf; // HACKER possibility here
        }
        Log::info('handleAppUninstalled 2', [
            'request' => $request->all(),
        ]);
        $this->leadSourceService->deleteLeadsSourceAccount([
            'api_name' => 'lightFunnels',
            'seller_id' => $id,
        ]);



        return response()->json([
            'response' => 'success',
        ], 200);
    }

    private function verifyLightfWebhook(Request $request)
    {
        return $this->lightfService->verifyWebhook($request);
    }

    private function getLightfSourceLead($sellerId)
    {
        return SellersApiImports::where('seller_id', $sellerId)
            ->where('api_name', 'lightFunnels')
            ->first();
    }

    /**
     * Check if the incoming order is from one of our stores.
     */
    private function isOrderFromOurStore(array $data, $sourceLead)
    {
        // If shopurl is 'All', then import from all stores
        if($sourceLead->shopurl === 'All'){
            return true;
        }
        $storeId = $data['node']['store_id'] ?? null;
        $allowedStores = explode(', ', $sourceLead->shopurl);
        return in_array($storeId, haystack: $allowedStores);
    }

    /**
     * Get store name from store ID.
     */
    private function getStoreName($storeId, $token)
    {
        $stores = $this->lightfService->getStores($token)['result'] ?? [];

        // fallback to 'slug' if 'defaultDomain' isn't available
        $mapped = array_map(function ($store) {
            return [
                'id' => $store['id'],
                'name' => $store['defaultDomain'] ?? ($store['slug'] ?? 'Unknown Store'),
            ];
        }, $stores);

        $storeMap = array_column($mapped, 'name', 'id');

        return $storeMap[$storeId] ?? 'Unknown Store';
    }

    /**
     * Update import stats
     */
    private function updateImportStats($sourceLead, $count)
    {
        $sourceLead->update([
            'last_imported_order' => $count,
            'last_imported_date' => now(),
        ]);
    }




}