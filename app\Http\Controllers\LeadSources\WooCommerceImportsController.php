<?php

namespace App\Http\Controllers\LeadSources;

use App\Http\Controllers\Controller;
use App\Models\Sellers;
use App\Models\SellersApiImports;
use App\Services\LeadSource\LeadSourceService;
use App\Services\LeadSource\WooCommerceService;
use App\Services\Orders\OrderAPIService;
use Illuminate\Support\Facades\Log;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Validator;
use SellersHelper as GlobalSellersHelper;

class WooCommerceImportsController extends Controller
{
    protected $woocommerceService;
    protected $currentSeller;
    protected $leadSourceService;
    protected $OrderAPIService;

    public function __construct()
    {
        $this->currentSeller = GlobalSellersHelper::CurrentSeller();
        $this->woocommerceService = new WooCommerceService();
        $this->leadSourceService = new LeadSourceService();
        $this->OrderAPIService = new OrderAPIService();
    }


    /**
     * Install WooCommerce Store - Validate credentials and return install URL
     */
    public function install(Request $request)
    {
        $validation = $this->woocommerceService->validateWooCommerceRequest($request);
        if ($validation['response'] === 'error') {
            return response()->json($validation, 400);
        }

        $storeUrl = $validation['store_url'];
        $consumerKey = $validation['consumer_key'];
        $consumerSecret = $validation['consumer_secret'];

        //check if lead exist
        $check = $this->leadSourceService->checkLeadExistance($storeUrl,'woocommerce',$this->currentSeller->id);
        if ($check['response'] === 'error') {
            return response()->json($check, 400);
        }

        // Register webhook
        $response = $this->callback($storeUrl, $consumerKey, $consumerSecret);
        return  $response;
    }

    /**
     * Toggle order status in WooCommerce
     *
     * @param Request $request
     * @param int $sellerId
     * @return \Illuminate\Http\JsonResponse
     */
    public function toggleStatus(Request $request, $sellerId)
    {
        $validation = $this->validateToggleStatusRequest($request);
        if ($validation['response'] === 'error') {
            return response()->json($validation, 400);
        }

        $status = $validation['result']['status'];
        $orderCode = $validation['result']['orderCode'];
        $expired = $validation['result']['expired'];
        $note = $validation['result']['note'];
        // Step 1: Retrieve API credentials
        $leadSourceResponse = $this->getWooCommerceCredentials($sellerId);
        if ($leadSourceResponse['response'] === 'error') {
            return response()->json($leadSourceResponse, 400);
        }
        $leadSource = $leadSourceResponse['result'];
        $wcStatus = $this->woocommerceService->mapToWooCommerceStatus($status, $expired);

        $orderUrl = "{$leadSource->shopurl}/wp-json/wc/v3/orders/{$orderCode}";

        // Step 2: Update order status
        $updateSuccess = $this->updateWooCommerceOrderStatus($orderUrl, $wcStatus, $leadSource->api_key, $leadSource->api_secretkey);
        if ($updateSuccess['response'] === 'error') {
            return response()->json($updateSuccess, 400);
        }


        // Step 3: Add note if present
        if (!empty($note)) {
            $noteResult = $this->addPrivateNoteToOrder($orderUrl, $note, $leadSource->api_key, $leadSource->api_secretkey);
            if ($noteResult['response'] === 'error') {
                return response()->json($noteResult, 400);
            }

        }

        return response()->json([
            'response' => 'success',
            'message' => 'Order status updated' . (!empty($note) ? ' and note added.' : '.'),
        ]);
    }

    /**
     * Validate the toggle status request.
     *
     * @param Request $request
     * @return array
     */
    private function validateToggleStatusRequest(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|string',
            'orderCode' => 'required|string',
            'expired' => 'boolean',
            'note' => 'nullable|string',
        ]);

        if ($validator->fails()) {
            return [
                'response' => 'error',
                'message' => 'Validation failed',
                'errors' => $validator->errors(),
            ];
        }

        return [
            'response' => 'success',
            'result' => $validator->validated(),
        ];
    }

    /**
     * Get WooCommerce API credentials for a seller.
     */
    private function getWooCommerceCredentials($sellerId)
    {
         $leadSource = SellersApiImports::where('seller_id', $sellerId)
            ->where('api_name', 'woocommerce')
            ->first();

        if (!$leadSource) {
            return ['response' => 'error',
                'message' => 'Lead source not found'];
        }

         // Check that API credentials are not null or empty
        if (empty($leadSource->api_key) || empty($leadSource->api_secretkey)) {
            return [
                'response' => 'error',
                'message' => 'WooCommerce API credentials are missing.',
            ];
        }
        return ['response' => 'success',
                'result' => $leadSource] ;
    }

    /**
     * Update WooCommerce order status using the API.
     */
    private function updateWooCommerceOrderStatus($orderUrl, $wcStatus, $consumerKey, $consumerSecret)
    {
        $response = Http::withBasicAuth($consumerKey, $consumerSecret)
            ->put($orderUrl, ['status' => $wcStatus]);

        if ($response->failed()) {
            return [
                'response' => 'error',
                'message' => $response->json(),
            ];
        }

        return [
            'response' => 'success',
        ];
    }

    /**
     * Add a private note to the WooCommerce order (visible only to store owner).
     */
    private function addPrivateNoteToOrder($orderUrl, $note, $consumerKey, $consumerSecret)
    {
        $notesUrl = "{$orderUrl}/notes";

        $response = Http::withBasicAuth($consumerKey, $consumerSecret)
            ->post($notesUrl, [
                'note' => "PowerGroup : {$note}",
                'customer_note' => false,
            ]);

        if ($response->failed()) {
            return [
                'response' => 'error',
                'message' => $response->json(),
            ];
        }

        return [
            'response' => 'success',
        ];
    }



    /**
     * Handle OAuth Callback - Get Access Token and Register Webhooks
     *
     * @param string $storeUrl
     * @param string $consumerKey
     * @param string $consumerSecret
     * @return
     */
    private function callback( $storeUrl, $consumerKey, $consumerSecret)
    {
        $webhookUrl = route('woocommerce.webhook', ['id' => $this->currentSeller->id]); //url('/api/v1'). "/woocommerce/order/created/".$this->currentSeller->id;
       $webhookResponse = $this->woocommerceService->registerWooWebhook($storeUrl, $consumerKey, $consumerSecret, 'order.created', $webhookUrl);

        if ($webhookResponse['response'] === 'error') {
            return response()->json($webhookResponse, 400);
        }

        //Add woocommere to Api Import
        $this->leadSourceService->createLeadSourceToken([
            'api_name' => 'woocommerce',
            'api_key' => $consumerKey,
            'api_secretkey' => $consumerSecret,
            'seller' => $this->currentSeller,
            'api_label' => preg_replace('#^(https?://)?(www\.)?#i', '', $storeUrl),
            'shopurl' => $storeUrl,
            'webhook' => json_encode($webhookResponse['result']),
        ]);

        return response()->json([
            'response' => 'success',
            'message' => 'WooCommerce lead source added successfully and webhooks registered.',
        ] , 200);

    }

    /**
     * Handle Order Webhook
     */
    public function handleOrderWebhook(Request $request,$id)
    {

        $request_from_woocommerce = $this->woocommerceService->validateWooCommerceRequestHeader($request, $id);

        if ($request_from_woocommerce['response'] === 'error') {
            return response()->json($request_from_woocommerce, 401);
        }

        $orderData = $this->woocommerceService->formatWebHookOrder($request->all(),
            $request_from_woocommerce['result']['storeUrl'],
            $request_from_woocommerce['result']['consumerKey'],
            $request_from_woocommerce['result']['consumerSecret']
        );

        $responseOrders = $this->OrderAPIService->sendOrders([
            'orderData' => $orderData,
            'sellerId' => $id,
        ]);

        // Check if the response contains an error
        if ($responseOrders['response'] === 'error') {
            Log::error('Order API Service returned an error', [
                'seller_id' => $id,
                'order_data' => $orderData,
                'response' => $responseOrders
            ]);
            return response()->json($responseOrders, 400);
        }

        //set lastImport date
        $this->updateWooLastImportDate($id, $request_from_woocommerce['result']['storeUrl'], count($orderData));

        // Return Response
        return response()->json($responseOrders, 200 );
    }


         /**
     * Update the last import date and order count for Shopify.
     *
     * @param int $sellerId
     * @param string $storeDomain
     * @param int $numberOfOrders
     * @return void
     */
    private function updateWooLastImportDate($sellerId, $storeDomain, $numberOfOrders)
    {

        SellersApiImports::where('seller_id', $sellerId)
            ->where('api_name', 'woocommerce')
            ->where('shopurl', rtrim($storeDomain, '/'))
            ->update([
                'last_imported_order' => $numberOfOrders,
                'last_imported_date' => now(),
            ]);

    }

}