<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class CheckServerApiToken
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Closure  $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $token = $request->header('token');

        // Get the server's IP (same as $_SERVER['SERVER_ADDR'])
        $serverIp = $request->server('SERVER_ADDR');

        // Get the real remote IP (Cloudflare-aware)
        $remoteIp = $request->header('cf-connecting-ip', $request->ip());

        // Allowed IPs array (can add more if needed)
        $allowedIps = [$serverIp];

        Log::info('fullfillment out', [
            'headers' => $request->headers->all(),
            'request' => $request->all(),
            'serverIp' => $serverIp,
            'remoteIp' => $remoteIp,
            'allowedIps' => $allowedIps,
        ]);

        if (!$token || !in_array($remoteIp, $allowedIps, true) || $token != env('SERVER_API_TOKEN')) {
            return response()->json([
                'response' => 'error',
                'message'  => 'Unauthorized. Token is missing.',
                'status'   => Response::HTTP_UNAUTHORIZED,
            ], Response::HTTP_UNAUTHORIZED);
        }

        return $next($request);
    }


}