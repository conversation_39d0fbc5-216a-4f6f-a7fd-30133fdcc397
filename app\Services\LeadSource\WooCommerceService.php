<?php

namespace App\Services\LeadSource;

use App\Models\SellersApiImports;
use GlobalHelper;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

class WooCommerceService
{

    /**
     * Validate the WooCommerce request.
     *
     * @param Request $request
     * @return array
     */
    public function validateWooCommerceRequest(Request $request)
    {
        $validated = Validator::make($request->all(), [
            'store_url' => 'required|string',
            'consumer_key' => 'required|string',
            'consumer_secret' => 'required|string',
        ]);

        if ($validated->fails()) {
            return [
                'response' => 'error',
                'message' => __('sourcing.missing_required_parameters'),
            ];
        }

        return [
            'response' => 'success',
            'store_url' => $validated->validated()['store_url'],
            'consumer_key' => $validated->validated()['consumer_key'],
            'consumer_secret' => $validated->validated()['consumer_secret'],
        ];
    }

    /**
     * Map the given status to WooCommerce status.
     *
     * @param string $status
     * @param bool $expired
     * @return string
     */
    public function mapToWooCommerceStatus(string $status, bool $expired = false): string
    {
        $statusMap = [
            'newlead' => 'on-hold',
            'pending' => 'on-hold',
            'schedule' => 'on-hold',
            'noanswer' => $expired ? 'failed' : 'on-hold',
            'cancelled' => 'cancelled',
            'duplicate' => 'failed',
            'wrongphonenumber' => 'failed',
            'test' => 'failed',
            'confirmed' => 'on-hold',
            'delivered' => 'completed',
            'return' => 'refunded',
            'intransit' => 'processing',
            'processing' => 'processing',
        ];

        return $statusMap[strtolower($status)] ?? 'on-hold';
    }


    /**
     * Register a webhook for WooCommerce
     *
     * @param string $storeUrl
     * @param string $consumerKey
     * @param string $consumerSecret
     * @param string $topic
     * @param string $deliveryUrl
     * @return array
        */
    public function registerWooWebhook(string $storeUrl, string $consumerKey, string $consumerSecret, string $topic, string $deliveryUrl)
    {
        $endpoint = rtrim($storeUrl, '/') . '/wp-json/wc/v3/webhooks';

        $data = [
            'name'         => 'Webhook for ' . $topic,
            'topic'        => $topic, // e.g., 'order.updated', 'order.created'
            'delivery_url' => $deliveryUrl,
            'secret'       => $consumerSecret,
        ];

        try {
            $response = Http::withBasicAuth($consumerKey, $consumerSecret)
                ->timeout(10)
                ->post($endpoint, $data);
                if (in_array($response->status(), [404, 405])) {
                    return [
                        'response' => 'error',
                        'message' => 'Wrong store URL.',
                    ];
                }

                if ($response->successful()) {
                    $contentType = $response->header('Content-Type');
                    if (str_contains($contentType, 'application/json')) {
                        return [
                            'response' => 'success',
                            'result' => $response->json(),
                        ];
                    } else {
                        return [
                            'response' => 'error',
                            'message' => 'The store URL may be incorrect or not a WooCommerce site.',
                        ];
                    }
                }

            if($response->status() === 401){
                return [
                    'response' => 'error',
                    'message' => $response->json()['message'] ?? 'Invalid key or secret.',
                ];
            }

            return [
                'response' => 'error',
                'message' => 'Failed to register webhook. Status: ' . $response->status(),
            ];

        } catch (\Exception $e) {

             // Check if it's a cURL SSL error
            if (strpos($e->getMessage(), 'cURL error 60') !== false) {
                return [
                    'response' => 'error',
                    'message' => 'Wrong store url.',
                ];
            }
            Log::error('Failed to register webhook for WooCommerce', [
                'store_url' => $storeUrl,
                'topic' => $topic,
                'delivery_url' => $deliveryUrl,
                'exception' => $e->getMessage(),
            ]);
            return [
                'response' => 'error',
                'message' => 'Exception: ' . $e->getMessage(),
            ];
        }
    }

    /**
     * Format a single order for export.
     *
     * @param array $dataRow
     * @return array
     */
    public function formatWebHookOrder($dataRow, $storeUrl = null, $consumerKey = null, $consumerSecret = null)
    {
        $lineItems = $dataRow['line_items'] ?? [];

        $skuDetailList = array_map(function ($item) {
            return [
                "name"   => GlobalHelper::RemoveEmoji($item['name'] ?? ''),
                "skuNo"  => $item['sku'] ?? null,
                "skuQty" => $item['quantity'] ?? 1,
            ];
        }, $lineItems);

        // Get product permalink if available
    $productLink = '';
        if (
            isset($lineItems[0]['product_id'], $storeUrl, $consumerKey, $consumerSecret)
        ) {
            $productLink = $this->getProductPermalink(
                $storeUrl,
                $consumerKey,
                $consumerSecret,
                $lineItems[0]['product_id']
            );
        }

        return [[
            "storeName"        => preg_replace('#^(https?://)?(www\.)?#i', '', parse_url($dataRow['payment_url'] ?? '', PHP_URL_HOST) ?? ''),
            "orderCode"        => $dataRow['number'] ?? null,
            "consigneeCountry" => $dataRow['shipping']['country'] ?? null,
            "consigneeContact" => GlobalHelper::RemoveEmoji(
                trim(($dataRow['billing']['first_name'] ?? '') . ' ' . ($dataRow['billing']['last_name'] ?? ''))
            ),
            "consigneeMobile"  => GlobalHelper::RemoveEmoji($dataRow['billing']['phone'] ?? '************'),
            "whatsappPhone"    => GlobalHelper::RemoveEmoji($dataRow['billing']['phone'] ?? '************'),
            "consigneeArea"    => GlobalHelper::RemoveEmoji($dataRow['shipping']['address_1'] ?? ''),
            "consigneeCity"    => GlobalHelper::RemoveEmoji($dataRow['shipping']['city'] ?? ''),
            "goodsDescription" => implode(' / ', array_map(function ($item) {
                return GlobalHelper::RemoveEmoji($item['name'] ?? '');
            }, $lineItems)),
            "productVaritante" => implode(' / ', array_map(function ($item) {
                // WooCommerce variation title is not directly provided, using empty string or meta data if needed
                return '';
            }, $lineItems)),
            "skuDetailList"    => $skuDetailList,
            "goodsValue"       => $dataRow['total'] ?? null,
            "currency"         => $dataRow['currency'] ?? null,
            "ProductLink" => $productLink ?: (
                isset($lineItems[0]['product_id'], $storeUrl)
                    ? rtrim($storeUrl, '/') . '/?post_type=product&p=' . $lineItems[0]['product_id']
                    : ''
            ),

            "comment_shipping" => GlobalHelper::RemoveEmoji($dataRow['shipping']['postcode'] ?? ''),
            "note"             => GlobalHelper::RemoveEmoji($dataRow['customer_note'] ?? ''),
            "orderSource"      => "woocommerce",
        ]];
    }

    /**
     * Get the product permalink for the given product ID.
     *
     * @param string $storeUrl
     * @param string $consumerKey
     * @param string $consumerSecret
     * @param int $productId
     * @return string|null
     */
    private function getProductPermalink(string $storeUrl, string $consumerKey, string $consumerSecret, int $productId): ?string
    {
        try {
            $endpoint = rtrim($storeUrl, '/') . "/wp-json/wc/v3/products/{$productId}";
            $response = Http::withBasicAuth($consumerKey, $consumerSecret)
                ->timeout(10)
                ->get($endpoint);

                Log::info('getProductPermalink', [
                'endpoint' => $endpoint,
                'response' => $response->json(),
            ]);

            if ($response->successful() && isset($response['permalink'])) {
                return $response['permalink'];
            }
        } catch (\Exception $e) {
            Log::error("Failed to fetch WooCommerce product permalink", [
                'store_url' => $storeUrl,
                'product_id' => $productId,
                'error' => $e->getMessage()
            ]);
        }

        return null;
    }


 /**
     * Validate the Woocommerce request.
     *
     * @param Request $request
     * @return array
     */
    public function validateWooCommerceRequestHeader(Request $request, $id)
    {
        $source = $request->header('x-wc-webhook-source');
            // Get WooCommerce secret for this seller (you must retrieve it from your DB or config)
        $Secrets = $this->getConsumerSecretForSeller($id,$source);

        if (!$Secrets || !isset($Secrets['api_secretkey'], $Secrets['api_key'])) {
            return [
                'response' => 'error',
                'message' => 'WooCommerce credentials not found for this seller or store.',
            ];
        }

        // Get the signature sent by WooCommerce in the header
        $signature = $request->header('x-wc-webhook-signature');

        // Get raw payload (body) as string
        $payload = $request->getContent();

        // Calculate HMAC SHA256 hash and base64 encode it
        $calculatedSignature = base64_encode(hash_hmac('sha256', $payload, $Secrets['api_secretkey'], true));


        if (!$signature) {

            return [
                'response' => 'error',
                'message' => 'Unauthorized webhook: missing signature',
            ];
        }

        // Compare signatures
        if (!hash_equals($signature, $calculatedSignature)) {
            Log::warning('WooCommerce webhook signature mismatch', [
                'expected' => $calculatedSignature,
                'received' => $signature,
            ]);
            return [
                'response' => 'error',
                'message' => 'Unauthorized webhook: signature mismatch',
            ];
        }
        return [
            'response' => 'success',
            'result' => [
                'storeUrl' => $request->header('x-wc-webhook-source'),
                'consumerSecret' => $Secrets['api_secretkey'],
                'consumerKey' => $Secrets['api_key'],
            ]
        ];
    }

    /**
     * Get the consumer secret for the given seller.
     *
     * @param int $id
     * @return array|null
     */
    private function getConsumerSecretForSeller($id,$source)
    {
        //remove last '/' from source
        $source = rtrim($source, '/');
        $record = SellersApiImports::where('seller_id', $id)
            ->where('api_name', 'woocommerce')
            ->where('shopurl', $source)
            ->whereNotNull('api_secretkey')
            ->orderByDesc('id') // or created_at if you prefer
            ->first();

        if (!$record) {
            Log::warning('No valid consumer secret found for seller', ['seller_id' => $id]);
            return null;
        }


        return [
            'api_key' => $record->api_key,
            'api_secretkey' => $record->api_secretkey,
        ];
    }

    public function deleteWebhook($storeUrl, $consumerKey, $consumerSecret, $webhookJson)
    {
            // Step 1: Decode the JSON string to extract the ID
            $webhookData = json_decode($webhookJson, true);
            $webhookId = $webhookData['id'] ?? null;

            if (!$webhookId) {
                return [
                    'response' => 'error',
                    'message' => 'Invalid webhook data: ID not found',
                ];
            }

            // Step 2: Build the specific webhook deletion URL
            $endpoint = rtrim($storeUrl, '/') . "/wp-json/wc/v3/webhooks/{$webhookId}?force=true";

        try {
            $response = Http::withBasicAuth($consumerKey, $consumerSecret)
                ->timeout(10)
                ->delete($endpoint);


            if ($response->successful()) {
                return [
                    'response' => 'success',
                ];
            }

            return [
                'response' => 'error',
                'message' => 'Failed to delete webhook. Status: ' . $response->status(),
            ];

        } catch (\Exception $e) {
            Log::error('Failed to delete webhook for WooCommerce', [
                'store_url' => $storeUrl,
                'exception' => $e->getMessage(),
            ]);
            return [
                'response' => 'error',
                'message' => 'Exception: ' . $e->getMessage(),
            ];
        }
    }


}